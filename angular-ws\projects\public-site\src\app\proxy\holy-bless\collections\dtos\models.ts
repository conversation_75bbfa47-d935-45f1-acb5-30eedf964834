import type { PublishStatus } from '../../enums/publish-status.enum';
import type { ArticleTitleDto } from '../../articles/dtos/models';
import type { EntityDto } from '@abp/ng.core';
import type { BucketFileDto } from '../../buckets/dtos/models';
import type { ListStyle } from '../../enums/list-style.enum';
import type { DefaultOrderByField } from '../../enums/default-order-by-field.enum';

export interface CollectionArticleTreeDto {
  id: number;
  name?: string;
  contentCode?: string;
  parentCollectionId?: number;
  description?: string;
  status?: PublishStatus;
  isRoot: boolean;
  children: CollectionArticleTreeDto[];
  articles: ArticleTitleDto[];
}

export interface CollectionDto extends EntityDto<number> {
  parentCollectionId?: number;
  contentCode?: string;
  languageCode?: string;
  thumbnailBucketFile: BucketFileDto;
  name?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  listStyle?: ListStyle;
  status?: PublishStatus;
  channelId?: number;
  channelName?: string;
  memo?: string;
  defaultOrderBy?: DefaultOrderByField;
}

export interface CollectionTreeDto {
  id: number;
  name?: string;
  contentCode?: string;
  parentCollectionId?: number;
  description?: string;
  status?: PublishStatus;
  isRoot: boolean;
  children: CollectionTreeDto[];
}
