:host {
  display: block;
  flex: 1;
  position: relative;
}

.input-tag-container:hover {
  border-color: var(--p-inputtext-hover-border-color);
}

.input-tag-container:focus-within {
  border-color: var(--p-inputtext-focus-border-color);
  box-shadow: var(--p-inputtext-focus-ring-shadow);
  outline: var(--p-inputtext-focus-ring-width)
    var(--p-inputtext-focus-ring-style) var(--p-inputtext-focus-ring-color);
  outline-offset: var(--p-inputtext-focus-ring-offset);
}
