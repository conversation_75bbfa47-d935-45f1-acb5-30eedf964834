import { ReadOnlyAlbumService } from '@/proxy/holy-bless/albums';
import { AlbumDto } from '@/proxy/holy-bless/albums/dtos';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { AlbumType } from '@/proxy/holy-bless/enums';
import { ChannelIdContentCodeService } from '@/services/channelIdContentCode.service';
import { I18nService } from '@/services/i18n.service';
import { LoadingService } from '@/services/loading.service';
import { CommonModule } from '@angular/common';
import { Component, HostListener, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { skip, Subscription } from 'rxjs';

@Component({
  selector: 'app-album-card',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule,
    FormsModule,
    PaginatorModule,
    ButtonModule,
  ],
  templateUrl: './album-card.html',
  styleUrls: ['./album-card.scss'],
})
export class AlbumCardComponent {
  router = inject(Router);
  route = inject(ActivatedRoute);
  #ReadOnlyAlbumService = inject(ReadOnlyAlbumService);

  audioFirst = 0;
  audioRows = 10;
  audioTotalRecords = 0;

  videoFirst = 0;
  videoRows = 10;
  videoTotalRecords = 0;

  private _isMobile = false;

  audioItems = signal<AlbumDto[]>([]);
  videoItems = signal<AlbumDto[]>([]);
  channelId: number | null = null;
  subs = new Subscription();
  i18nService = inject(I18nService);
  channelIdContentCodeService = inject(ChannelIdContentCodeService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  loadingService = inject(LoadingService);

  constructor() {
    this.checkMobile();
  }

  ngOnInit() {
    this.route.params.subscribe({
      next: (param) => {
        this.channelId = +param['channelId'];
        this.loadAudio();
        this.loadVideo();
        this.changeLanguage(this.channelId);
      },
    });
  }

  changeLanguage(channelId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe(() => {
      const contentCode =
        this.channelIdContentCodeService.getChannelIdContentCode(channelId);
      if (contentCode) {
        this.#ReadOnlyChannelService
          .getMatchedChannelByContentCode(contentCode)
          .subscribe({
            next: (channel) => {
              if (!channel) {
                this.router.navigateByUrl('/landing');
                return;
              }
              if (channel.id !== channelId) {
                this.router.navigateByUrl(`/podcast/album-card/${channel.id}`);
              }
            },
          });
      }
    });
    this.subs.add(sub);
  }

  loadAudio() {
    if (!this.channelId) return;
    this.loadingService.show();
    this.#ReadOnlyAlbumService
      .getList({
        channelId: this.channelId,
        skipCount: this.audioFirst,
        maxResultCount: this.audioRows,
        albumType: AlbumType.Audio,
      })
      .subscribe({
        next: (data) => {
          this.audioItems.set(data.items || []);
          this.audioTotalRecords = data.totalCount || 0;
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取专辑数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  loadVideo() {
    if (!this.channelId) return;
    this.loadingService.show();
    this.#ReadOnlyAlbumService
      .getList({
        channelId: this.channelId,
        skipCount: this.videoFirst,
        maxResultCount: this.videoRows,
        albumType: AlbumType.Video,
      })
      .subscribe({
        next: (data) => {
          this.videoItems.set(data.items || []);
          this.videoTotalRecords = data.totalCount || 0;
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取专辑数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10, 20, 50];
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  openBook(albumId: number | undefined) {
    this.router.navigateByUrl(`/podcast/album-detail/${albumId}`);
  }

  onAudioPageChange(event: any) {
    this.audioFirst = event.first;
    this.audioRows = event.rows;
    this.loadAudio();
  }

  onVideoPageChange(event: any) {
    this.videoFirst = event.first;
    this.videoRows = event.rows;
    this.loadVideo();
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
