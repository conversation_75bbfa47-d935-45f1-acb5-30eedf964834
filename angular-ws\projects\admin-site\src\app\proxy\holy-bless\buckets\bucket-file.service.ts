import type { BucketFileAdminSearchDto, BucketFileDto, BucketFileSearchDto, CreateUpdateBucketFileDto, FileSearchResultDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class BucketFileService {
  apiName = 'Default';
  

  create = (input: CreateUpdateBucketFileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BucketFileDto>({
      method: 'POST',
      url: '/api/app/bucket-file',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/bucket-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BucketFileDto>({
      method: 'GET',
      url: `/api/app/bucket-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: BucketFileAdminSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<BucketFileDto>>({
      method: 'GET',
      url: '/api/app/bucket-file',
      params: { fileName: input.fileName, languageCode: input.languageCode, spokenLangCode: input.spokenLangCode, deliveryDateStart: input.deliveryDateStart, deliveryDateEnd: input.deliveryDateEnd, contentCategories: input.contentCategories, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  search = (input: BucketFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<FileSearchResultDto>>({
      method: 'POST',
      url: '/api/app/bucket-file/search',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateBucketFileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, BucketFileDto>({
      method: 'PUT',
      url: `/api/app/bucket-file/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
