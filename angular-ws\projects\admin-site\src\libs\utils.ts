import { cloneDeep } from 'lodash-es';

export const BuildTreeData = (
  flatData: any[],
  parentKey = 'parentChannelId',
  key = 'id',
  label = 'name',
  map = new Map(),
) => {
  const result: any[] = [];

  flatData.forEach((item) => {
    map.set(item[key], {
      ...item,
      key: item[key],
      label: item[label],
      children: [],
    });
  });

  flatData.forEach((item) => {
    const node = map.get(item[key]);

    if (!item[parentKey]) {
      result.push(node);
    } else {
      const parent = map.get(item[parentKey]);
      if (parent) {
        parent.children.push(node);
      }
    }
  });
  return result;
};

export const BuildFlatData = (data: any[], list: any[]) => {
  data.forEach((item) => {
    const _item = cloneDeep(item);
    delete _item.children;
    list.push(_item);
    if (item.children && item.children.length) {
      BuildFlatData(item.children, list);
    }
  });
};
