import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.holybless.sglz',
  appName: 'holybless',
  webDir: 'dist/public-site/browser',

  // 平台特定配置
  android: {
    allowMixedContent: false,
    // 其他Android特定配置
  },
  ios: {
    scrollEnabled: true,
    // 其他iOS特定配置
  },

  // 插件配置
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      backgroundColor: '#ffffffff',
      // 其他 splash 屏幕配置
    },
  },
};

export default config;
