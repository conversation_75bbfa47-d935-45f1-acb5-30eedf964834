import { inject, Injectable, signal } from '@angular/core';
import { VideoCacheService } from './video-cache.service';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class DownloadService {
  #VideoCacheService = inject(VideoCacheService);
  progress = signal(50);
  showProgress = signal(false);

  async downloadFile(url: string, fileName: string) {
    if (!url) return;
    const cached = await this.#VideoCacheService.getCachedVideo(url);
    let downloadUrl = url;
    if (cached) {
      downloadUrl = cached.blob ? URL.createObjectURL(cached.blob) : url;
    }
    try {
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = fileName || this.extractFileName(downloadUrl) || 'download';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      if (!cached) {
        await this.#VideoCacheService.smartCacheVideo(downloadUrl, a.download);
      }
    } catch (e) {
      console.error('Download failed:', e);
      // 如果创建下载链接失败，直接打开URL
      window.open(downloadUrl, '_blank');
    }
  }

  private extractFileName(downloadUrl: string): string {
    try {
      const url = new URL(downloadUrl);
      const pathname = url.pathname;
      const fileName = pathname.split('/').pop();
      return fileName || 'download';
    } catch {
      return downloadUrl.split('/').pop() || 'download';
    }
  }

  async batchDownloadAsZip(
    files: { fileUrl: string; fileName: string }[],
    zipName: string = 'batch-download.zip',
  ) {
    if (files.length === 0) return;
    this.showProgress.set(true);
    this.progress.set(0);
    const zip = new JSZip();

    try {
      let completedFiles = 0;
      const totalFiles = files.length;
      // 并发下载所有文件
      const downloadPromises = files.map(async ({ fileUrl, fileName }) => {
        try {
          await this.#VideoCacheService.smartCacheVideo(fileUrl, fileName);
          const cached = await this.#VideoCacheService.getCachedVideo(fileUrl);
          completedFiles++;
          const progress = Math.round((completedFiles / totalFiles) * 80); // 80% 用于下载，20% 用于打包
          this.progress.set(progress);
          if (cached) {
            return { fileName, blob: cached.blob };
          }
          return null;
        } catch (error) {
          console.error(`Failed to download ${fileName}:`, error);
          completedFiles++;
          const progress = Math.round((completedFiles / totalFiles) * 80);
          this.progress.set(progress);
          return null;
        }
      });

      const results = await Promise.allSettled(downloadPromises);

      this.progress.set(85);

      // 将成功下载的文件添加到 ZIP
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          const { fileName, blob } = result.value;
          zip.file(fileName, blob);
        }
      });

      this.progress.set(95);

      // 生成并下载 ZIP 文件
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      this.progress.set(100);
      setTimeout(() => {
        this.showProgress.set(false);
        this.progress.set(0);
      }, 5000);
      saveAs(zipBlob, zipName);
    } catch (error) {
      console.error('Batch download failed:', error);
    }
  }
}
