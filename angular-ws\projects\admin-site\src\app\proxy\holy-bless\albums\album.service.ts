import type { AlbumAggregateDto, AlbumDto, AlbumFileDto, AlbumSearchDto, CreateUpdateAlbumDto, CreateUpdateAlbumToFileDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class AlbumService {
  apiName = 'Default';
  

  addFilesToAlbum = (albumId: number, files: CreateUpdateAlbumToFileDto[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumFileDto[]>({
      method: 'POST',
      url: `/api/app/album/files-to-album/${albumId}`,
      body: files,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateUpdateAlbumDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumDto>({
      method: 'POST',
      url: '/api/app/album',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/album/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumDto>({
      method: 'GET',
      url: `/api/app/album/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAlbumFiles = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumAggregateDto>({
      method: 'GET',
      url: `/api/app/album/album-files/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  getAlbumsByChannel = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumDto[]>({
      method: 'GET',
      url: `/api/app/album/albums-by-channel/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getAllAlbums = (languageCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumDto[]>({
      method: 'GET',
      url: '/api/app/album/albums',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: AlbumSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<AlbumDto>>({
      method: 'GET',
      url: '/api/app/album',
      params: { channelId: input.channelId, albumType: input.albumType, channelContentCode: input.channelContentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndAlbumId = (channelId: number, albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/album/link-to-channel',
      params: { channelId, albumId },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndAlbumIds = (channelId: number, albumIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/album/link-to-channel/${channelId}`,
      body: albumIds,
    },
    { apiName: this.apiName,...config });
  

  moveAlbum = (albumId: number, beforeId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/album/move-album',
      params: { albumId, beforeId },
    },
    { apiName: this.apiName,...config });
  

  moveAlbumFile = (fileId: number, beforeFileId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/album/move-album-file',
      params: { fileId, beforeFileId },
    },
    { apiName: this.apiName,...config });
  

  removeFileFromAlbum = (albumId: number, fileId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/album/file-from-album',
      params: { albumId, fileId },
    },
    { apiName: this.apiName,...config });
  

  reorderAlbumFiles = (albumId: number, fileIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/album/reorder-album-files/${albumId}`,
      body: fileIds,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByAlbumId = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/album/unlink-from-channel/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByAlbumIds = (albumIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/album/unlink-from-channel',
      body: albumIds,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateAlbumDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumDto>({
      method: 'PUT',
      url: `/api/app/album/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });
  

  updateAlbumFile = (input: CreateUpdateAlbumToFileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, AlbumFileDto>({
      method: 'PUT',
      url: '/api/app/album/album-file',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
