import { Component, computed, inject, signal } from '@angular/core';
import { CTreeTableComponent } from '../../components/c-tree-table/c-tree-table';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { VirtualFolderService } from '@/proxy/holy-bless/virtual-folders/virtual-folder.service';
import { FormsModule } from '@angular/forms';
import { SelectButtonModule } from 'primeng/selectbutton';
import { DrawerModule } from 'primeng/drawer';
import { ChannelService } from '@/proxy/holy-bless/channels/channel.service';
import { ScrollerModule } from 'primeng/scroller';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';
import { InputTextModule } from 'primeng/inputtext';
import { TreeSelectModule } from 'primeng/treeselect';
import { SelectModule } from 'primeng/select';
import { cloneDeep } from 'lodash-es';
import { spokenLangList } from '../../libs/constants';
import { TableModule } from 'primeng/table';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-folder',
  standalone: true,
  templateUrl: './folder.html',
  styleUrls: ['./folder.scss'],
  imports: [
    CTreeTableComponent,
    ButtonModule,
    DialogModule,
    FormsModule,
    SelectButtonModule,
    DrawerModule,
    ScrollerModule,
    CheckboxModule,
    DatePickerModule,
    InputTextModule,
    TreeSelectModule,
    SelectModule,
    TableModule,
  ],
})
export class FolderComponent {
  #VirtualFolderService = inject(VirtualFolderService);
  #ChannelService = inject(ChannelService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);

  // lang
  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');

  spokenLangList = computed(() =>
    spokenLangList.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  // table
  data = signal<any[]>([]);
  cols = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID'), width: '8rem'  },
    { field: 'folderName', header: this.i18nService.t()('FolderName') },
    { field: 'channelName', header: this.i18nService.t()('Channel') },
    {
      field: 'spokenLangCode',
      header: this.i18nService.t()('SpokenLanguage'),
      type: 'select',
      selectData: this.spokenLangList(),
    },
  ]);

  // folder dialog
  mode = signal<'edit' | 'create'>('create');
  selectData = signal<any>(null);

  channelTree = signal<any[]>([]);
  channelMap = new Map();

  // drawer
  drawerVisible = signal(false);
  subData = signal<any[]>([]);
  selectSub = signal<any[]>([]);

  // file dialog
  fileDialogVisible = signal(false);
  items = signal<{ label: string; value: string; date: string }[]>([]);
  filterItems = computed(() => {
    return this.items().filter((item) => {
      const matchesId = item.value.includes(this.searchId());
      const matchesName = item.label
        .toLowerCase()
        .includes(this.searchName().toLowerCase());
      const matchesDate = this.searchDate()
        ? this.searchDate()
            ?.toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })
            .replace(/\//g, '-') === item.date
        : true;
      return matchesId && matchesName && matchesDate;
    });
  });
  selectedItems = signal<{ label: string; value: string; date: string }[]>([]);
  searchId = signal('');
  searchName = signal('');
  searchDate = signal<Date | null>(null);

  ngOnInit() {
    this.loadData();
    this.loadChannelData();
    for (let i = 1; i <= 1000; i++) {
      this.items().push({
        label: `name${i}`,
        value: `${i}`,
        date: '2023-01-01',
      });
    }
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#VirtualFolderService.getAllVirtualFolders(this.lang()).subscribe({
      next: (res) => {
        this.data.set(res);
        this.#LoadingService.loading.set(false);
      },
      error: () => {
        this.#LoadingService.loading.set(false);
      },
    });
  }

  loadChannelData() {
    this.#ChannelService.getAllChannels(this.lang()).subscribe({
      next: (res) => {
        this.channelTree.set(
          this.buildTreeData(
            res,
            undefined,
            undefined,
            undefined,
            this.channelMap,
          ),
        );
      },
    });
  }

  buildTreeData(
    flatData: any[],
    parentKey = 'parentChannelId',
    key = 'id',
    label = 'name',
    map = new Map(),
  ) {
    const result: any[] = [];

    flatData.forEach((item) => {
      map.set(item[key], {
        ...item,
        key: item[key],
        label: item[label],
        children: [],
      });
    });

    flatData.forEach((item) => {
      const node = map.get(item[key]);

      if (!item[parentKey]) {
        result.push(node);
      } else {
        const parent = map.get(item[parentKey]);
        if (parent) {
          parent.children.push(node);
        }
      }
    });
    return result;
  }

  handleDrop({ channelId, toParentId, beforeId }: any) {
    this.#VirtualFolderService
      .moveVirtualFolder(channelId, toParentId, beforeId)
      .subscribe({
        next: (res) => {
          this.loadData();
        },
      });
  }

  handleCreate() {
    this.mode.set('create');
    this.selectData.set({});
  }

  handleEdit(rowData: any) {
    this.mode.set('edit');
    const _rowData = cloneDeep(rowData);
    if (_rowData.channelId) {
      _rowData.channel = {
        key: _rowData.channelId,
        label: this.channelMap.get(_rowData.channelId)?.name,
      };
    }
    this.selectData.set(_rowData);
  }

  handleAdd(rowData: any) {
    this.drawerVisible.set(true);
    this.#VirtualFolderService
      .getAllFolderFilesByFolderId(rowData.id)
      .subscribe({
        next: (res) => {
          this.subData.set(res || []);
        },
      });
  }

  handleAddFile() {
    this.fileDialogVisible.set(true);
  }

  updateSelectChannel() {
    const _selectData = cloneDeep(this.selectData());
    if (_selectData.channel) {
      _selectData.channelId = _selectData.channel.id;
      delete _selectData.channel;
    }
    if (this.mode() === 'edit') {
      this.#VirtualFolderService.update(_selectData.id, _selectData).subscribe({
        next: (res) => {
          this.loadData();
        },
      });
    } else {
      this.#VirtualFolderService.create(_selectData).subscribe({
        next: (res) => {
          this.loadData();
        },
      });
    }
  }

  handleDelete(rowData: any) {
    this.#VirtualFolderService.delete(rowData.id).subscribe({
      next: (res) => {
        this.loadData();
      },
    });
  }

  resetFilters() {
    this.searchName.set('');
    this.searchDate.set(null);
  }

  handleRemove() {}
}
