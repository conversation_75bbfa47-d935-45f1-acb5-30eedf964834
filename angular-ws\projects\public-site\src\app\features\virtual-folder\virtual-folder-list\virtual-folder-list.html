<div class="p-6">
  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
    @for (item of items(); track item.id) {
      <div class="cursor-pointer" (click)="navigateToFolderDetail(item.id)">
        <p class="flex justify-center mb-4">
          <img src="assets/images/folder.png" alt="" />
        </p>
        <p class="flex justify-center" [innerHTML]="item.folderName"></p>
      </div>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first()"
      [rows]="rows()"
      [totalRecords]="totalRecords()"
      [showPageLinks]="!(mobileService.isMobile | async)"
      [showCurrentPageReport]="mobileService.isMobile | async"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
