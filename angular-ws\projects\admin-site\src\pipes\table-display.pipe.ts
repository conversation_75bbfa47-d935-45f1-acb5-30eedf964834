import { formatDate } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'tableDisplay',
  standalone: true,
})
export class TableDisplayPipe implements PipeTransform {
  transform(
    value: any,
    {
      type = '',
      selectData = [],
    }: { type: string; selectData: { key: string; value: any }[] },
  ): string {
    if (type === 'date') {
      if (!value) return value;
      return formatDate(value, 'yyyy-MM-dd', 'en-US');
    }
    if (type === 'select') {
      const foundItem: any = selectData.find(
        (item: any) => item.value === value,
      );
      return foundItem ? foundItem.key : value;
    }
    return value;
  }
}
