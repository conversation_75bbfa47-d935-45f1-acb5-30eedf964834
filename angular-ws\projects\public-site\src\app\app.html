<p-blockUI [blocked]="loadingService.loading$ | async" class="custom-loading">
  <div class="w-full flex flex-col items-center justify-center h-screen">
    <i class="pi pi-spin pi-spinner text-[#fff]" style="font-size: 4rem"></i>
    <p class="mt-4 text-white">{{ i18nService.translate('loading') }}...</p>
  </div>
</p-blockUI>
<div class="app-container">
  <!-- Navigation Menu Component -->
  <app-navigation-menu></app-navigation-menu>

  <!-- 主要内容区域 -->
  <main class="app-content">
    <router-outlet></router-outlet>
  </main>
</div>

<!-- Drawer 组件 -->
<app-setting-drawer></app-setting-drawer>
<app-player-drawer></app-player-drawer>


<app-download-progress></app-download-progress>