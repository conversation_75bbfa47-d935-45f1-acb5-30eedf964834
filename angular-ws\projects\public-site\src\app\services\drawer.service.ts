import { Injectable, signal } from '@angular/core';

export interface DrawerConfig {
  type: 'settings' | 'playlist' | 'custom';
  title: string;
  width?: string;
  position?: 'left' | 'right' | 'top' | 'bottom';
  modal?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class DrawerService {
  settingVisible = signal(false);
  playerVisible = signal(false);
  playerList = signal<any[]>([]);

  openSettings() {
    this.settingVisible.set(true);
  }

  closeSettings() {
    this.settingVisible.set(false);
  }

  openPlayer() {
    this.playerVisible.set(true);
  }

  closePlayer() {
    this.playerVisible.set(false);
  }
}
