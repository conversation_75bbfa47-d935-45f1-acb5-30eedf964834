import {
  Component,
  OnInit,
  signal,
  computed,
  inject,
  HostListener,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MenubarModule } from 'primeng/menubar';
import { MenuItem } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { TooltipModule } from 'primeng/tooltip';
import { MenuModule } from 'primeng/menu';
import { DrawerService } from '../../services/drawer.service';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { ChannelTreeDto } from '@/proxy/holy-bless/channels/dtos';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ChannelSource, ListStyle } from '@/proxy/holy-bless/enums';
import { I18nService, Language } from '@/services/i18n.service';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { ThemeService } from '@/services/theme.service';
import { Subscription } from 'rxjs';
import { ChannelIdContentCodeService } from '@/services/channelIdContentCode.service';
import { LoadingService } from '@/services/loading.service';
import { MobileService } from '@/services/mobile.service';

@Component({
  selector: 'app-navigation-menu',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MenubarModule,
    ButtonModule,
    DropdownModule,
    TooltipModule,
    MenuModule,
  ],
  templateUrl: './navigation-menu.component.html',
  styleUrls: ['./navigation-menu.component.scss'],
})
export class NavigationMenuComponent implements OnInit {
  i18nService = inject(I18nService);
  drawerService = inject(DrawerService);
  themeService = inject(ThemeService);
  channelIdContentCodeService = inject(ChannelIdContentCodeService);
  subs = new Subscription();
  loadingService = inject(LoadingService);
  mobileService = inject(MobileService);

  menuItems: MenuItem[] = [];
  activeItem = signal<MenuItem | null>(null);

  displayLanguages = computed((): MenuItem[] =>
    this.i18nService.supportedLanguages.map((lang) => ({
      label: lang.label,
      command: () => this.selectDisplayLanguage(lang),
    })),
  );

  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  router = inject(Router);

  ngOnInit() {
    const menuItems = localStorage.getItem('menuItems')
      ? JSON.parse(localStorage.getItem('menuItems') || '[]')
      : [];
    this.menuItems = menuItems;
    const sub = this.i18nService.language$.subscribe((lang) => {
      this.loadChannelTree();
    });
    this.subs.add(sub);
  }

  loadChannelTree() {
    this.loadingService.show();
    this.#ReadOnlyChannelService
      .getChannelTree(this.i18nService.language())
      .subscribe({
        next: (data) => {
          const menuItems = this.initializeMenuItems(data);
          menuItems.push({
            label: this.i18nService.translate('search'),
            command: () => this.router.navigateByUrl('/search'),
          });
          this.menuItems = menuItems;
          localStorage.setItem(
            'menuItems',
            JSON.stringify(this.menuItems || []),
          );
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取频道树数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  private initializeMenuItems(data: ChannelTreeDto[]): MenuItem[] {
    return data.map((channel) => {
      this.channelIdContentCodeService.setChannelIdContentCode(
        channel.id,
        channel.contentCode || '',
      );
      return {
        label: this.i18nService.translate(channel.name || ''),
        items: this.initializeMenuItems(channel.children),
        command: !channel.children.length
          ? this.navigateToChannel.bind(this, channel)
          : undefined,
      };
    });
  }

  navigateToChannel(channel: ChannelTreeDto) {
    switch (channel.channelSource) {
      case ChannelSource.VirtualDisk:
        this.activeItem.set(null);
        this.router.navigateByUrl(`/virtual-folder/list/${channel.id}`);
        break;
      case ChannelSource.Ebook:
        this.activeItem.set(null);
        this.router.navigateByUrl(`/ebooks/ebook-card/${channel.id}`);
        break;
      case ChannelSource.PodCast:
        this.activeItem.set(null);
        this.router.navigateByUrl(`/podcast/album-card/${channel.id}`);
        break;
      case ChannelSource.Collection:
        this.activeItem.set(null);
        this.navigateToCollection(channel);
        break;
    }
  }

  navigateToCollection(channel: ChannelTreeDto) {
    this.#ReadOnlyCollectionService.getFirstByChannelId(channel.id).subscribe({
      next: (res) => {
        if (!res.id) return;
        switch (res.listStyle) {
          case ListStyle.ImageCard:
            this.router.navigateByUrl(`/home/<USER>/${res.id}`);
            break;
          case ListStyle.SummaryCard:
            this.router.navigateByUrl(`/home/<USER>/${res.id}`);
            break;
          case ListStyle.ArticleTree:
            this.router.navigateByUrl(`/home/<USER>/${res.id}`);
            break;
          case ListStyle.CollectionTree:
            this.router.navigateByUrl(`/home/<USER>/${res.id}`);
            break;
          case ListStyle.CollectionArticleTree:
            this.router.navigateByUrl(
              `/home/<USER>/${res.id}`,
            );
            break;
        }
      },
    });
  }

  navigateToHome() {
    this.router.navigate(['/landing']);
  }

  selectDisplayLanguage(language: Language) {
    this.i18nService.setLanguage(language.code);
  }

  toggleSubMenu(item: MenuItem) {
    this.activeItem.set(item);
  }

  elementRef = inject(ElementRef);

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const isClickInside = this.elementRef.nativeElement.contains(target);
    if (!isClickInside) {
      this.activeItem.set(null);
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  handleMenuItem(item: MenuItem) {
    if (item.items && item.items.length > 0) {
      this.toggleSubMenu(item);
    } else {
      if (item.command) {
        item.command({});
      }
    }
  }
}
