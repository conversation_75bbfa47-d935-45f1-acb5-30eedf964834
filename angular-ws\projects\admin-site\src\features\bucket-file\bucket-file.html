<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <div class="flex gap-2">
      <p-button
        [text]="true"
        [label]="i18nService.t()('Create')"
        (onClick)="handleCreate()"
      ></p-button>
      <p-button
        [text]="true"
        severity="danger"
        [label]="i18nService.t()('Delete')"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToFolder')"
        (onClick)="handleAddToFolder()"
        [disabled]="!selectedRows().length"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToAlbum')"
        (onClick)="handleAddToAlbum()"
        [disabled]="!selectedRows().length"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToArticle')"
        (onClick)="handleAddToArticle()"
        [disabled]="!selectedRows().length"
      ></p-button>
    </div>
  </div>
  <p-table
    [value]="data()"
    [paginator]="true"
    [first]="first()"
    [rows]="rows()"
    [columns]="columns()"
    [totalRecords]="totalRecords()"
    [lazy]="true"
    (onPage)="onPageChange($event)"
    [showCurrentPageReport]="true"
    stripedRows
    [(selection)]="selectedRows"
    (onFilter)="handleFilter($event)"
  >
    <ng-template #header let-columns>
      <tr>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        @for(col of columns; track col.field) {
        <th pReorderableColumn style="width: 4rem">
          <div class="flex items-center">
            {{col.header}} @if(col.filter) {
            <p-columnFilter
              [type]="col.type || 'text'"
              [field]="col.field"
              display="menu"
              [matchModeOptions]="getMatchModeOptions(col.type)"
              [showAddButton]="col.type === 'date'"
              [showOperator]="false"
              [matchMode]="col.matchMode || 'contains'"
              [showApplyButton]="!col.hideApplyButton"
            >
              @if(col.type === 'select') {
              <ng-template #filter let-value let-filter="filterCallback">
                <p-select
                  styleClass="w-full"
                  [ngModel]="value"
                  [options]="col.selectData"
                  (ngModelChange)="filter($event)"
                  optionLabel="key"
                  optionValue="value"
                  placeholder="select"
                  appendTo="body"
                />
              </ng-template>
              }
            </p-columnFilter>
            }
          </div>
        </th>
        }
        <th style="width: 7rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        @for(col of columns; track col.field) {
        <td>
          <div class="truncate max-w-[20rem]">
            {{rowData[col.field] | tableDisplay: {type: col.type, selectData:
            col.selectData} }}
          </div>
        </td>
        }
        <td>
          <p-button
            icon="pi pi-pencil"
            [text]="true"
            styleClass="!rounded-full"
            (onClick)="handleEdit(rowData)"
          ></p-button>
          <p-button
            icon="pi pi-trash"
            [text]="true"
            severity="danger"
            styleClass="!rounded-full"
            (onClick)="handleDelete($event, rowData)"
          ></p-button>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-confirmpopup />

<p-dialog
  [header]="mode() === 'edit' ? i18nService.t()('Edit') : i18nService.t()('Create')"
  [modal]="true"
  [(visible)]="selectData"
  [style]="{ width: '25rem' }"
>
  @if(selectData()) {
  <div class="form">
    <div class="form-item">
      <label>{{ i18nService.t()('FileName') }}</label>
      <input type="text" pInputText [(ngModel)]="selectData().fileName" />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('RelativePath') }}</label>
      <input
        type="text"
        pInputText
        [(ngModel)]="selectData().relativePathInBucket"
      />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('ContentCategory') }}</label>
      <p-select
        [options]="contentCategoryOptions()"
        [(ngModel)]="selectData().contentCategory"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('DeliveryDate') }}</label>
      <p-datepicker
        styleClass="w-full"
        [(ngModel)]="selectData().deliveryDate"
      />
    </div>
  </div>
  }
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button styleClass="w-full" severity="secondary">
      {{ i18nService.t()("Cancel") }}
    </p-button>
    <p-button styleClass="w-full" (onClick)="updateSelectData()">
      {{ i18nService.t()("Confirm") }}
    </p-button>
  </div>
</p-dialog>

<p-dialog
  [header]="i18nService.t()('AddToFolder')"
  [modal]="true"
  [(visible)]="addToFolderVisible"
  [style]="{ width: '25rem' }"
>
  <div class="form">
    <div class="form-item">
      <label>{{ i18nService.t()('Folder') }}</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectFolder"
        [options]="folderTree()"
        appendTo="body"
      />
    </div>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button styleClass="w-full" severity="secondary">
      {{ i18nService.t()("Cancel") }}
    </p-button>
    <p-button styleClass="w-full" (onClick)="handleChangeFolder()">
      {{ i18nService.t()("Confirm") }}
    </p-button>
  </div>
</p-dialog>

<p-dialog
  [header]="i18nService.t()('AddToAlbum')"
  [modal]="true"
  [(visible)]="addToAlbumVisible"
  [style]="{ width: '25rem' }"
>
  <div class="form">
    <div class="form-item">
      <label> {{ i18nService.t()('Album') }} </label>
      <p-select
        [options]="albumList()"
        [(ngModel)]="selectAlbum"
        optionLabel="title"
        optionValue="id"
        appendTo="body"
      />
    </div>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button styleClass="w-full" severity="secondary">
      {{ i18nService.t()("Cancel") }}
    </p-button>
    <p-button styleClass="w-full" (onClick)="handleChangeAlbum()">
      {{ i18nService.t()("Confirm") }}
    </p-button>
  </div>
</p-dialog>

<p-dialog
  [header]="i18nService.t()('AddToArticle')"
  [modal]="true"
  [(visible)]="addToArticleVisible"
  [style]="{ width: '60vw' }"
>
  <div>
    <p-table
      [value]="articleList()"
      [paginator]="true"
      [first]="articleFirst()"
      [rows]="articleRows()"
      [columns]="columns()"
      [totalRecords]="articleTotalRecords()"
      [lazy]="true"
      (onPage)="onArticlePageChange($event)"
      [showCurrentPageReport]="true"
      stripedRows
    >
      <ng-template #header let-columns>
        <tr>
          <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
          <th>{{ i18nService.t()('ID') }}</th>
          <th>{{ i18nService.t()('Title') }}</th>
        </tr>
      </ng-template>
      <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
        <tr>
          <td>
            <p-tableCheckbox [value]="rowData" />
          </td>
          <td>{{rowData.id}}</td>
          <td>{{rowData.title}}</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button styleClass="w-full" severity="secondary">
      {{ i18nService.t()("Cancel") }}
    </p-button>
    <p-button styleClass="w-full" (onClick)="handleChangeArticle()">
      {{ i18nService.t()("Confirm") }}
    </p-button>
  </div>
</p-dialog>
