import { CommonModule } from '@angular/common';
import {
  Component,
  effect,
  EventEmitter,
  Input,
  Output,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DrawerModule } from 'primeng/drawer';

@Component({
  selector: 'app-catalog',
  standalone: true,
  templateUrl: './catalog.html',
  styleUrls: ['./catalog.scss'],
  imports: [ButtonModule, DrawerModule, CommonModule, FormsModule],
})
export class CatalogComponent {
  @Input() catalogList: any[] = [];
  @Output() onItemClick = new EventEmitter<any>();
  visible = signal(false);
  show = signal(false);

  constructor() {
    effect(() => {
      if (!this.visible()) {
        setTimeout(() => {
          this.show.set(false);
        }, 5000);
      }
    });

    effect(() => {
      if (this.show()) {
        setTimeout(() => {
          this.show.set(false);
        }, 5000);
      }
    });
  }

  btnClick() {
    if (!this.show()) {
      this.show.set(true);
      return;
    }
    this.visible.set(!this.visible());
  }

  itemClick(item: any) {
    this.onItemClick.emit(item);
    this.visible.set(false);
  }
}
