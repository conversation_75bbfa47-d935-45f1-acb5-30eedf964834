<div class="h-[6rem] overflow-y-hidden">
  <ul
    class="w-full flex flex-col transition-transform duration-300 ease-in-out"
    [style.transform]="'translateY(-' + offset() + 'px)'"
  >
    @for (item of lrcData; track $index) {
    <li
      class="flex justify-center text-white/50 py-1"
      [ngClass]="{'!text-white': activeIndex() === $index}"
    >
      {{ item.text }}
    </li>
    }
  </ul>
</div>
