<div class="flex flex-1 flex-col md:flex-row">
  @if(mobileService.isMobile | async) {
  <app-mobile-catagory-files-drawer
    [leftTemplate]="treeTemplate"
    [rightTemplate]="galleriaTemplate"
    [leftDrawerVisible]="leftDrawerVisible"
  />
  } @else {
  <ng-container *ngTemplateOutlet="treeTemplate"></ng-container>
  }
  <div class="articaldetail-container prose max-w-none p-6 flex-1">
    <h1 class="text-3xl font-bold mb-4">{{ articleDetail()?.title }}</h1>
    <p class="text-sm mb-4 flex items-center gap-2">
      @if (articleDetail()) {
      <i class="pi pi-clock"></i>
      {{ articleDetail()?.deliveryDate | date: 'yyyy-MM-dd' }} }
    </p>
    <p
      class="mb-4 main-content-text"
      [innerHTML]="articleDetail()?.content"
    ></p>
  </div>
  <div class="p-6 max-w-[20rem]">
    @if(!(mobileService.isMobile | async)) {
    <ng-container *ngTemplateOutlet="galleriaTemplate"></ng-container>
    }
  </div>
</div>

<ng-template #galleriaTemplate>
  <div>
    <div>
      <p-galleria
        [value]="imageArticleFiles()"
        indicatorsPosition="right"
        [showIndicators]="true"
        [showThumbnails]="false"
        [showIndicatorsOnItem]="true"
        [containerStyle]="{'width': '100%','margin-top': '2em'}"
      >
        <ng-template pTemplate="item" let-item>
          <img [src]="item.fileUrl" class="w-72 h-96 rounded-lg shadow-lg" />
        </ng-template>
      </p-galleria>
    </div>
    <app-play-download
      [primaryArticleFiles]="primaryArticleFiles()"
      [notImageArticleFiles]="notImageArticleFiles()"
    ></app-play-download>
  </div>
</ng-template>

<ng-template #treeTemplate>
  <p-tree
    [value]="files"
    [styleClass]="`${(mobileService.isMobile | async) ? 'w-full' : 'w-[20rem]'} h-full`"
    selectionMode="single"
    [(selection)]="selectedFile"
    (onNodeSelect)="loadArticalDetail($event.node)"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
  />
</ng-template>
