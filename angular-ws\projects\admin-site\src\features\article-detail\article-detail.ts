import { Component, inject, Input, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { ChipModule } from 'primeng/chip';
import { ArticleService } from '@/proxy/holy-bless/articles/article.service';
import { ArticleDto } from '@/proxy/holy-bless/articles/dtos';
import { EditorModule } from 'primeng/editor';
import { articleContentCategoryOptions } from '@/proxy/holy-bless/enums/article-content-category.enum';
import { SelectModule } from 'primeng/select';
import { publishStatusOptions } from '@/proxy/holy-bless/enums/publish-status.enum';
import { DatePickerModule } from 'primeng/datepicker';
import { ButtonModule } from 'primeng/button';
import { cloneDeep } from 'lodash-es';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-article-detail',
  templateUrl: './article-detail.html',
  styleUrls: ['./article-detail.scss'],
  standalone: true,
  imports: [
    FormsModule,
    InputTextModule,
    TextareaModule,
    ChipModule,
    EditorModule,
    SelectModule,
    DatePickerModule,
    ButtonModule,
  ],
})
export class ArticleDetailComponent {
  route = inject(ActivatedRoute);
  router = inject(Router);
  #ArticleService = inject(ArticleService);
  i18nService = inject(I18nService)

  articleContentCategoryOptions = articleContentCategoryOptions;
  publishStatusOptions = publishStatusOptions;
  data = signal<any>({});
  id = '';

  constructor() {
    const id = this.route.snapshot.paramMap.get('id');
    this.id = id || 'create';
    this.loadData(id || 'create');
  }

  loadData(id: string | 'create') {
    if (id === 'create') return;
    this.#ArticleService.get(+id).subscribe({
      next: (res) => {
        if (res.deliveryDate) {
          res.deliveryDate = new Date(res.deliveryDate) as any;
        }
        if (res.keywords) {
          res.keywords = res.keywords.split(',') as any;
        }
        this.data.set(res);
      },
    });
  }

  removeKeyword(index: number) {
    const keywords = this.data().keywords || [];
    keywords.splice(index, 1);
    this.data.set({ ...this.data(), keywords });
  }

  handleAddKeyword(event: Event) {
    const input = event.target as HTMLInputElement;
    const value = input.value.trim();
    if (value) {
      const keywords = this.data().keywords || [];
      keywords.push(value);
      this.data.set({ ...this.data(), keywords });
      input.value = '';
    }
  }

  handleCancel() {
    this.router.navigateByUrl(`/article`);
  }

  handleUpdate() {
    const data = cloneDeep(this.data());
    data.keywords = data.keywords.join(',');
    if (data.deliveryDate) {
      data.deliveryDate = (data.deliveryDate as Date).toISOString();
    }
    if (this.id === 'create') {
      this.#ArticleService.create(data).subscribe({
        next: (res) => {
          this.router.navigateByUrl(`/article`);
        },
      });
    } else {
      this.#ArticleService.update(this.data().id, data).subscribe({
        next: (res) => {
          this.router.navigateByUrl(`/article`);
        },
      });
    }
  }
}
