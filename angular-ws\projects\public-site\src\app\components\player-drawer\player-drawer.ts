import {
  Component,
  inject,
  signal,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerModule } from 'primeng/drawer';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { DrawerService } from '../../services/drawer.service';
import { MenuModule } from 'primeng/menu';
import { I18nService } from '@/services/i18n.service';
import { ThemeService } from '@/services/theme.service';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { LyricScrollComponent } from '../lyricScroll/lyricScroll';
import { PlayerService } from '@/services/player.service';
import { DrawerComponent } from '../drawer/drawer.component';
import { VideoCacheService } from '@/services/video-cache.service';
import { SliderModule } from 'primeng/slider';

@Component({
  selector: 'app-player-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    DividerModule,
    ButtonModule,
    MenuModule,
    RemoveExtensionPipe,
    LyricScrollComponent,
    DrawerComponent,
    SliderModule,
  ],
  templateUrl: './player-drawer.html',
  styleUrls: ['./player-drawer.scss'],
})
export class PlayerDrawerComponent {
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('volumeTrack') volumeTrack!: ElementRef<HTMLDivElement>;
  @ViewChild('volumeHandle') volumeHandle!: ElementRef<HTMLDivElement>;

  currentTime = signal(0); // 当前播放时间
  duration = signal(0); // 视频总时长
  isPlaying = signal(false); // 是否正在播放

  // 注入 DrawerService
  drawerService = inject(DrawerService);
  i18nService = inject(I18nService);
  themeService = inject(ThemeService);
  playerService = inject(PlayerService);
  cacheVideoService = inject(VideoCacheService);
  playMode = signal<'loop' | 'single' | 'random'>('loop'); // 播放模式

  // 音量状态
  volume = signal(70); // 默认音量70%
  isMuted = signal(false);
  private lastVolume = 70; // 保存静音前的音量
  private isDragging = false;

  // 点击轨道设置音量
  onVolumeTrackClick(event: MouseEvent) {
    if (this.isDragging) return;

    const track = this.volumeTrack.nativeElement;
    const rect = track.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const trackWidth = rect.width;

    // 计算点击位置对应的音量百分比
    const newVolume = Math.round((clickX / trackWidth) * 100);
    this.setVolume(Math.max(0, Math.min(100, newVolume)));
  }

  onVolumeHandleMouseDown(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    this.isDragging = true;
    const track = this.volumeTrack.nativeElement;
    const handle = this.volumeHandle.nativeElement;

    // 添加拖拽样式
    handle.classList.add('scale-125');

    const handleMouseMove = (e: MouseEvent) => {
      const rect = track.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const trackWidth = rect.width;

      // 计算新的音量百分比
      let newVolume = (mouseX / trackWidth) * 100;

      // 限制范围在0-100之间
      newVolume = Math.max(0, Math.min(100, newVolume));

      this.setVolume(Math.round(newVolume));
    };

    const handleMouseUp = () => {
      this.isDragging = false;
      handle.classList.remove('scale-125');

      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleMouseUp);
    };

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleMouseUp);
  }

  // 设置音量
  setVolume(vol: number) {
    const newVolume = Math.max(0, Math.min(100, vol));
    this.volume.set(newVolume);
    this.updateVideoVolume();

    // 如果音量大于0，取消静音状态
    if (newVolume > 0 && this.isMuted()) {
      this.isMuted.set(false);
    }
  }
  // 切换静音
  toggleMute() {
    if (this.isMuted()) {
      // 取消静音，恢复之前的音量
      this.isMuted.set(false);
      this.setVolume(this.lastVolume);
    } else {
      // 静音，保存当前音量
      this.lastVolume = this.volume();
      this.isMuted.set(true);
      this.setVolume(0);
    }
  }

  // 更新视频元素的音量
  private updateVideoVolume() {
    if (this.videoElement?.nativeElement) {
      this.videoElement.nativeElement.volume = this.volume() / 100;
      this.videoElement.nativeElement.muted = this.isMuted();
    }
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateVideoVolume();
      this.#setupVideoEventListeners();
    });
    this.playerService.activeItem$.subscribe((activeItem) => {
      if (activeItem) {
        this.playVideo(activeItem);
      }
    });
  }

  #setupVideoEventListeners() {
    const video = this.videoElement.nativeElement;
    this.playerService.drawerService;
    video.addEventListener('timeupdate', () => {
      this.currentTime.set(video.currentTime);
      this.duration.set(video.duration);
    });
  }

  tooglePlay() {
    const video = this.videoElement.nativeElement;
    if (this.isPlaying()) {
      video.pause();
      this.isPlaying.set(false);
    } else {
      video.play();
      this.isPlaying.set(true);
    }
  }

  changePlayMode() {
    if (this.playMode() === 'loop') {
      this.playMode.set('single');
    } else if (this.playMode() === 'single') {
      this.playMode.set('random');
    } else {
      this.playMode.set('loop');
    }
  }

  activeItem = signal<any>({});

  async playVideo(item: any) {
    const cache = await this.cacheVideoService.getCachedVideo(item.fileUrl);
    if (cache) {
      this.videoElement.nativeElement.src = URL.createObjectURL(cache.blob);
    } else {
      this.videoElement.nativeElement.src = item.fileUrl;
      this.cacheVideoService.smartCacheVideo(item.fileUrl, item.fileName);
    }
    this.isPlaying.set(true);
    this.activeItem.set(item);
  }

  playNext() {
    if (this.playMode() === 'single') {
      return;
    } else if (this.playMode() === 'loop') {
      const list = this.playerService.getPlayerList();
      if (list.length <= 1) {
        return;
      }
      const findIndex = list.findIndex(
        (l) => l.fileId === this.activeItem().fileId,
      );
      if (findIndex === -1) {
        return;
      }
      let nextIndex = (findIndex + 1) % list.length;
      this.playVideo(list[nextIndex]);
    } else if (this.playMode() === 'random') {
      const list = this.playerService.getPlayerList();
      if (list.length <= 1) {
        return;
      }
      const randomIndex = Math.floor(Math.random() * list.length);
      this.playVideo(list[randomIndex]);
    }
  }
}
