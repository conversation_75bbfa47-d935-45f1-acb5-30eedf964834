import {
  Component,
  Input,
  Output,
  EventEmitter,
  HostListener,
  signal,
  computed,
  effect,
  ElementRef,
  ViewChild,
  OnInit,
  OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { trigger, state, style, transition, animate } from '@angular/animations';

export type DrawerPosition = 'left' | 'right' | 'top' | 'bottom';
export type DrawerSize = 'small' | 'medium' | 'large' | 'full';

@Component({
  selector: 'app-drawer',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- 遮罩层 -->
    <div 
      *ngIf="visible() && modal"
      class="drawer-overlay"
      [class.show]="showOverlay()"
      (click)="onOverlayClick()"
      [@fadeInOut]="showOverlay() ? 'in' : 'out'"
    ></div>

    <!-- 抽屉容器 -->
    <div 
      #drawerContainer
      class="drawer-container"
      [class]="drawerClasses()"
      [style]="drawerStyles()"
      [@slideInOut]="animationState()"
      (@slideInOut.done)="onAnimationDone($event)"
    >
      <!-- 抽屉内容 -->
      <div class="drawer-content" [style.padding]="contentPadding">
        <ng-content></ng-content>
      </div>

      <!-- 抽屉底部 -->
      <div *ngIf="hasFooterContent" class="drawer-footer">
        <ng-content select="[slot=footer]"></ng-content>
      </div>
    </div>
  `,
  styleUrls: ['./drawer.component.scss'],
  animations: [
    trigger('slideInOut', [
      // 左侧抽屉动画状态
      state('left-in', style({ transform: 'translateX(0)' })),
      state('left-out', style({ transform: 'translateX(-100%)' })),
      
      // 右侧抽屉动画状态
      state('right-in', style({ transform: 'translateX(0)' })),
      state('right-out', style({ transform: 'translateX(100%)' })),
      
      // 顶部抽屉动画状态
      state('top-in', style({ transform: 'translateY(0)' })),
      state('top-out', style({ transform: 'translateY(-100%)' })),
      
      // 底部抽屉动画状态
      state('bottom-in', style({ transform: 'translateY(0)' })),
      state('bottom-out', style({ transform: 'translateY(100%)' })),

      // 具体的转换动画 - 左侧
      transition('left-out => left-in', animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      transition('left-in => left-out', animate('250ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      
      // 具体的转换动画 - 右侧
      transition('right-out => right-in', animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      transition('right-in => right-out', animate('250ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      
      // 具体的转换动画 - 顶部
      transition('top-out => top-in', animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      transition('top-in => top-out', animate('250ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      
      // 具体的转换动画 - 底部
      transition('bottom-out => bottom-in', animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)')),
      transition('bottom-in => bottom-out', animate('250ms cubic-bezier(0.25, 0.8, 0.25, 1)'))
    ]),
    trigger('fadeInOut', [
      state('in', style({ opacity: 1 })),
      state('out', style({ opacity: 0 })),
      transition('out => in', animate('200ms ease-in')),
      transition('in => out', animate('150ms ease-out'))
    ])
  ]
})
export class DrawerComponent implements OnInit, OnDestroy {
  @ViewChild('drawerContainer') drawerContainer!: ElementRef<HTMLElement>;

  // 基础属性
  @Input() visible = signal(false);
  @Input() position: DrawerPosition = 'right';
  @Input() size: DrawerSize = 'medium';
  @Input() modal = true;
  @Input() dismissible = true;
  @Input() closeOnEscape = true;
  @Input() blockScroll = true;
  @Input() appendTo: HTMLElement | null = null;

  // 样式属性
  @Input() width: string = '';
  @Input() height: string = '';
  @Input() styleClass: string = '';
  @Input() contentPadding: string = '1rem';

  // 头部属性
  @Input() showHeader = true;
  @Input() showCloseIcon = true;
  @Input() title: string = '';

  // 内容检测
  hasHeaderContent = false;
  hasFooterContent = false;

  // 事件
  @Output() onShow = new EventEmitter<void>();
  @Output() onHide = new EventEmitter<void>();
  @Output() onAnimationStart = new EventEmitter<any>();
  @Output() onAnimationDoneEmit = new EventEmitter<any>();

  // 内部状态
  showOverlay = signal(false);
  isAnimating = signal(false);

  // 计算属性
  animationState = computed(() => {
    const pos = this.position;
    const isVisible = this.visible();
    return `${pos}-${isVisible ? 'in' : 'out'}`;
  });

  drawerClasses = computed(() => {
    const classes = [
      'drawer',
      `drawer-${this.position}`,
      `drawer-${this.size}`
    ];
    
    if (this.styleClass) {
      classes.push(this.styleClass);
    }
    
    if (this.modal) {
      classes.push('drawer-modal');
    }
    
    if (this.dismissible) {
      classes.push('drawer-dismissible');
    }
    
    return classes.join(' ');
  });

  drawerStyles = computed(() => {
    const styles: any = {};
    
    if (this.width) styles.width = this.width;
    if (this.height) styles.height = this.height;
    
    return styles;
  });

  constructor() {
    // 监听 visible 变化
    effect(() => {
      const isVisible = this.visible();
      if (isVisible) {
        this.show();
      } else {
        this.hide();
      }
    });
  }

  ngOnInit() {
    this.checkContentProjection();
  }

  ngOnDestroy() {
    this.restoreBodyScroll();
  }

  private checkContentProjection() {
    // 在实际项目中，你可能需要使用 ContentChild 来检测投影内容
    this.hasHeaderContent = false; // 简化实现
    this.hasFooterContent = false; // 简化实现
  }

  show() {
    if (this.isAnimating()) return;
    
    this.onShow.emit();
    this.showOverlay.set(true);
    
    if (this.blockScroll) {
      this.preventBodyScroll();
    }
    
    if (this.appendTo) {
      this.appendTo.appendChild(this.drawerContainer?.nativeElement);
    }
  }

  hide() {
    if (this.isAnimating()) return;
    
    this.onHide.emit();
    this.showOverlay.set(false);
    this.restoreBodyScroll();
  }

  close() {
    this.visible.set(false);
  }

  onOverlayClick() {
    if (this.dismissible) {
      this.close();
    }
  }

  onAnimationDone(event: any) {
    this.isAnimating.set(false);
    this.onAnimationDoneEmit.emit(event);
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent) {
    if (this.closeOnEscape && this.visible() && this.dismissible) {
      this.close();
    }
  }

  private preventBodyScroll() {
    document.body.style.overflow = 'hidden';
  }

  private restoreBodyScroll() {
    document.body.style.overflow = '';
  }
}