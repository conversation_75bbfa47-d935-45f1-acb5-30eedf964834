import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TabsModule } from 'primeng/tabs';
import { InputTextModule } from 'primeng/inputtext';
import { CheckboxModule } from 'primeng/checkbox';
import { SelectModule } from 'primeng/select';
import { DatePickerModule } from 'primeng/datepicker';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { MultiSelectModule } from 'primeng/multiselect';
import { ArticleSearchResultDto } from '@/proxy/holy-bless/articles/dtos';
import { LoadingService } from '@/services/loading.service';
import { ReadOnlyBucketFileService } from '@/proxy/holy-bless/buckets';
import { FileSearchResultDto } from '@/proxy/holy-bless/buckets/dtos';
import {
  articleContentCategoryOptions,
  contentCategoryOptions,
} from '@/proxy/holy-bless/enums';
import { I18nService } from '@/services/i18n.service';
import { DownloadService } from '@/services/download.service';
import { PlayerService } from '@/services/player.service';

@Component({
  selector: 'app-search',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    TabsModule,
    InputTextModule,
    CheckboxModule,
    SelectModule,
    DatePickerModule,
    ButtonModule,
    TableModule,
    RemoveExtensionPipe,
    MultiSelectModule,
  ],
  templateUrl: './search.html',
  styleUrls: ['./search.scss'],
})
export class SearchComponent {
  activeTab: number = 0;
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  #ReadOnlyBucketFileService = inject(ReadOnlyBucketFileService);
  loadingService = inject(LoadingService);
  i18nService = inject(I18nService);
  downloadService = inject(DownloadService);
  playerService = inject(PlayerService);

  first = signal(0);
  rows = signal(10);
  totalRecords = signal(0);
  selectedProducts: any[] = [];

  keyword: string = '';
  searchFields = [];
  articleContentCategoryOptions = computed(() =>
    articleContentCategoryOptions.map((item) => ({
      ...item,
      name: this.i18nService.translate(item.key),
    })),
  );
  contentCategoryOptions = computed(() =>
    contentCategoryOptions.map((item) => ({
      ...item,
      name: this.i18nService.translate(item.key),
    })),
  );

  articleContentCategoryMap = computed(() =>
    this.articleContentCategoryOptions().reduce((acc: any, item) => {
      acc[item.value] = item.key;
      return acc;
    }, {}),
  );

  contentCategoryMap = computed(() =>
    this.contentCategoryOptions().reduce((acc: any, item: any) => {
      acc[item.value] = item.key;
      return acc;
    }, {}),
  );

  articleContentCategories = [];
  fileContentCategories = [];
  deliveryDateStart: Date | null = null;
  deliveryDateEnd: Date | null = null;

  dataList: (ArticleSearchResultDto | FileSearchResultDto)[] = [];

  searchArticles() {
    this.loadingService.show();
    this.#ReadOnlyArticleService
      .search({
        keyword: this.keyword,
        searchFields: this.searchFields,
        contentCategories: this.articleContentCategories,
        deliveryDateStart: this.deliveryDateStart?.toISOString(),
        deliveryDateEnd: this.deliveryDateEnd?.toISOString(),
        skipCount: this.first(),
        maxResultCount: this.rows(),
      })
      .subscribe({
        next: (res) => {
          this.dataList = res.items || [];
          this.totalRecords.set(res.totalCount || 0);
          this.loadingService.hide();
        },
        error: (err) => {
          console.error('Search failed', err);
          this.loadingService.hide();
        },
      });
  }

  searchFiles() {
    this.loadingService.show();
    this.#ReadOnlyBucketFileService
      .search({
        fileName: this.keyword,
        deliveryDateStart: this.deliveryDateStart?.toISOString(),
        deliveryDateEnd: this.deliveryDateEnd?.toISOString(),
        contentCategories: this.fileContentCategories,
        skipCount: this.first(),
        maxResultCount: this.rows(),
      })
      .subscribe({
        next: (res) => {
          this.dataList = res.items || [];
          this.totalRecords.set(res.totalCount || 0);
          this.loadingService.hide();
        },
        error: (err) => {
          console.error('Search failed', err);
          this.loadingService.hide();
        },
      });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.rows.set(event.rows);
    if (this.activeTab === 0) {
      this.searchArticles();
    } else if (this.activeTab === 1) {
      this.searchFiles();
    }
  }
}
