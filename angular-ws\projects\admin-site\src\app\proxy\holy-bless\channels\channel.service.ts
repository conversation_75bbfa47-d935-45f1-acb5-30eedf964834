import type { ChannelDto, ChannelSearchDto, ChannelTreeDto, CreateUpdateChannelDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ChannelService {
  apiName = 'Default';
  

  create = (input: CreateUpdateChannelDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'POST',
      url: '/api/app/channel',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, forceDelete: boolean = true, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/channel/${id}`,
      params: { forceDelete },
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/channel/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllChannels = (languageCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto[]>({
      method: 'GET',
      url: '/api/app/channel/channels',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getChannelTree = (languageCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelTreeDto[]>({
      method: 'GET',
      url: '/api/app/channel/channel-tree',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: ChannelSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ChannelDto>>({
      method: 'GET',
      url: '/api/app/channel',
      params: { contentCode: input.contentCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannel = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/channel/matched-channel/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByAlbumId = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/channel/matched-channel-by-album-id/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByBookId = (bookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/channel/matched-channel-by-book-id/${bookId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: '/api/app/channel/matched-channel-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByVirtualFolderId = (virtualFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/channel/matched-channel-by-virtual-folder-id/${virtualFolderId}`,
    },
    { apiName: this.apiName,...config });
  

  moveChannel = (channelId: number, toParentId: number, beforeId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/channel/move-channel',
      params: { channelId, toParentId, beforeId },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateChannelDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'PUT',
      url: `/api/app/channel/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
