<div class="flex flex-1 flex-col md:flex-row">
  @if((mobileService.isMobile | async)) {
  <app-mobile-catagory-files-drawer
    [leftTemplate]="treeTemplate"
    [showRightIcon]="false"
    [leftDrawerVisible]="leftDrawerVisible"
  />
  } @else {
  <ng-container *ngTemplateOutlet="treeTemplate"></ng-container>
  }
  <div
    class="flex-1 overflow-y-auto"
    [class.w-screen]="mobileService.isMobile | async"
    style="height: calc(100vh - 5rem)"
  >
    @if(items().length === 0) {
      <p [innerHTML]="bookDetail().description" class="p-6"></p>
    } @else {
    <p-accordion value="0" [multiple]="true">
      @for (item of items(); track $index) {
      <p-accordion-panel [value]="item.id">
        <p-accordion-header>
          <p class="flex-1 flex justify-between items-center">
            <span>{{ item.title }}</span>
            <p-button
              icon="pi pi-play-circle"
              [text]="true"
              (click)="onPlayClick($event, item)"
            />
          </p>
        </p-accordion-header>
        <p-accordion-content>
          <p class="m-0 main-content-text" [innerHTML]="item.content"></p>
        </p-accordion-content>
      </p-accordion-panel>
      }
    </p-accordion>
    }
  </div>
</div>

<ng-template #treeTemplate>
  <p-tree
    [value]="files()"
    [styleClass]="`${(mobileService.isMobile | async) ? 'w-full' : 'w-[20rem]'} h-full`"
    selectionMode="single"
    [(selection)]="selectedFile"
    (onNodeSelect)="onFileSelect($event.node)"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
  />
</ng-template>
