import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'removeExtension'
})
export class RemoveExtensionPipe implements PipeTransform {
  transform(filename: string | undefined): string {
    if (!filename) {
      return '';
    }
    
    const lastDotIndex = filename.lastIndexOf('.');
    
    // 如果没有找到点号，或者点号在开头（隐藏文件），返回原文件名
    if (lastDotIndex <= 0) {
      return filename;
    }
    
    return filename.substring(0, lastDotIndex);
  }
}