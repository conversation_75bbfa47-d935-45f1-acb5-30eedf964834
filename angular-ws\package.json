{"name": "angular-ws", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:public": "ng serve public-site", "build": "ng build", "watch": "ng build --watch --configuration development", "sp-proxy-public": "abp generate-proxy --type ng --source public-site --output-path projects/public-site/src/app/proxy -u https://localhost:44362", "format": "prettier --write \"**/*.{js,mjs,ts,mts,d.ts,html}\" --cache", "test": "ng test", "test:coverage": "ng test --no-watch --code-coverage", "test:public-site": "ng test public-site --no-watch --code-coverage", "test:shared-lib": "ng test shared-lib --no-watch --code-coverage", "lint": "ng lint", "prepare": "husky", "pre-commit": "lint-staged"}, "private": true, "dependencies": {"@abp/ng.core": "~9.2.3", "@abp/ng.oauth": "~9.2.3", "@angular/animations": "^19.2.0", "@angular/cdk": "19.2.3", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/localize": "^19.2.8", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@angular/service-worker": "^19.2.0", "@capacitor/android": "^7.4.3", "@capacitor/cli": "^7.4.3", "@capacitor/core": "^7.4.3", "@capacitor/ios": "^7.4.3", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.0.6", "@types/video.js": "^7.3.58", "chart.js": "4.4.2", "dexie": "^4.0.11", "file-saver": "^2.0.5", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "primeicons": "^7.0.0", "primeng": "^19.1.3", "quill": "^2.0.3", "rxjs": "~7.8.0", "tailwindcss-primeui": "^0.6.1", "tslib": "^2.3.0", "uuid": "^11.1.0", "video.js": "^8.23.3", "zone.js": "~0.15.0"}, "devDependencies": {"@abp/ng.schematics": "~9.2.3", "@angular-devkit/build-angular": "^19.2.0", "@angular-eslint/builder": "^19.2.0", "@angular-eslint/eslint-plugin": "^19.2.0", "@angular-eslint/eslint-plugin-template": "^19.2.0", "@angular-eslint/schematics": "^19.2.0", "@angular-eslint/template-parser": "^19.2.0", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "@angular/language-service": "^19.2.10", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "~20.11.24", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^16.0.0", "ng-packagr": "^19.2.2", "postcss": "^8.4.49", "prettier": "^3.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.6.3"}}