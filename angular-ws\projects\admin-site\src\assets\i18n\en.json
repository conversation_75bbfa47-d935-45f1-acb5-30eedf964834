{"Article": "Article", "Album": "Album", "AddToFolder": "Add to Folder", "AddToAlbum": "Add to Album", "AddToArticle": "Add to Article", "AddToCollection": "Add to Collection", "AddToChannel": "Add to Channel", "AddToChapter": "Add to Chapter", "AddFile": "Add File", "ArticleTitle": "Title", "ArticleContent": "Content", "ArticleDescription": "Description", "ArticleKeywords": "Keywords", "BucketFile": "File", "Collection": "Collection", "Chapter": "Chapter", "Channel": "Channel", "ChannelSource": "Channel Source", "ContentCategory": "Content Category", "ContentReviewed": "Reviewed", "ContentCode": "Content Code", "ChapterContent": "Introduction", "Create": "Create", "Cancel": "Cancel", "Confirm": "Confirm", "DeliveryDate": "Date", "DoYouWantToDeleteThisRecord": "Are you sure you want to delete this record?", "Delete": "Delete", "Description": "Description", "Ebook": "Ebook", "Edit": "Edit", "FileName": "File Name", "Folder": "Folder", "FolderName": "Folder Name", "ParentChapter": "Parent Chapter", "ParentCollection": "Parent Collection", "RelativePath": "Relative Path", "Remove": "Remove", "Save": "Save", "Status": "Status", "SpokenLanguage": "Language", "Title": "Title", "Type": "Type", "Thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "ID": "ID", "VirtualDisk": "Virtual Disk", "Views": "Views", "zh-Hans": "Simplified Chinese", "zh-Hant": "Traditional Chinese", "en": "English", "Name": "Name", "ListStyle": "Page Style", "Keywords": "Keywords", "Mandarin": "Mandarin", "Cantonese": "Cantonese", "English": "English", "Image": "Image", "Document": "Document", "OriginalAudio": "Original Audio", "OriginalVideo": "Original Video", "NonOriginalAudio": "Non-Original Audio", "NonOriginalVideo": "Non-Original Video", "Package": "Package", "Draft": "Draft", "Published": "Published", "Lecture": "Lecture", "Remark": "Q&A", "StudentArticle": "Student Article", "Extract": "Extract", "Audio": "Audio", "Video": "Video", "Unspefied": "Unspecified", "PodCast": "Podcast", "LastModifierId(Readonly)": "Last Modifier (<PERSON><PERSON><PERSON>)"}