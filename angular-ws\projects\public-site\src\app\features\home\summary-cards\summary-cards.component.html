<div class="summary-cards-container p-6">
  <p-breadcrumb [model]="breadcrumbItems()" [home]="home"> </p-breadcrumb>
  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
      <p-card styleClass="card-item" [id]="item.id">
        <p
          class="p-card-title cursor-pointer !text-[1.1rem] !font-semibold"
          (click)="navigateToArticle(item)"
          [innerHTML]="item.title"
        ></p>
        <p
          class="mt-2 text-gray-500 text-sm line-clamp-2"
          [innerHTML]="
            item.description || '<span class=\'italic\'>暂无内容</span>'
          "
        ></p>
        <ng-template pTemplate="footer">
          <div class="text-gray-500 text-sm flex items-center">
            {{ item.creationTime | date: "yyyy-MM-dd" }}
          </div>
        </ng-template>
      </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
