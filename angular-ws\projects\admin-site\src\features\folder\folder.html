<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <p-button [label]="i18nService.t()('Create')" [text]="true" (onClick)="handleCreate()"></p-button>
  </div>
  <c-tree-table
    [data]="data"
    [cols]="cols()"
    parentKey="parentFolderId"
    (onDrop)="handleDrop($event)"
    (onEdit)="handleEdit($event)"
    (onAdd)="handleAdd($event)"
    (onDelete)="handleDelete($event)"
  ></c-tree-table>
</div>

<p-dialog
  [header]="mode() === 'edit' ? i18nService.t()('Edit') : i18nService.t()('Create')"
  [modal]="true"
  [(visible)]="selectData"
  [style]="{ width: '50vw' }"
>
  @if(selectData()) {
  <div class="form">
    <div class="form-item">
      <label>
        {{i18nService.t()('Name')}}
      </label>
      <input
        type="text"
        pInputText
        [(ngModel)]="selectData().folderName"
      />
    </div>
    <div class="form-item">
      <label>
        {{i18nService.t()('Channel')}}
      </label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectData().channel"
        [options]="channelTree()"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>
        {{i18nService.t()('SpokenLanguage')}}
      </label>
      <p-select
        [options]="spokenLangList()"
        [(ngModel)]="selectData().spokenLangCode"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
  </div>
  }
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="updateSelectChannel()"
      [label]="i18nService.t()('Confirm')"
    >
    </p-button>
  </div>
</p-dialog>

<p-drawer
  [(visible)]="drawerVisible"
  position="right"
  [style]="{ width: '40rem' }"
>
  <p-table
    [value]="subData()"
    [reorderableColumns]="true"
    [(selection)]="selectSub"
    stripedRows
  >
    <ng-template #header>
      <tr>
        <th style="width: 4rem"></th>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th style="width: 4rem">
          {{i18nService.t()('ID')}}
        </th>
        <th>{{i18nService.t()('FileName')}}</th>
        <th>{{i18nService.t()('Title')}}</th>
        <th>
          <p-button
            [text]="true"
            icon="pi pi-trash"
            [label]="i18nService.t()('Remove')"
            severity="danger"
            (onClick)="handleRemove()"
          ></p-button>
        </th>
      </tr>
    </ng-template>
    <ng-template
      #body
      let-rowData
      let-columns="columns"
      let-index="rowIndex"
      let-editing="editing"
    >
      <tr [pReorderableRow]="index">
        <td>
          <span class="pi pi-bars" pReorderableRowHandle></span>
        </td>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        <td>{{rowData.fileId}}</td>
        <td>{{rowData.fileName}}</td>
        <td [pEditableColumn]="rowData.title" pEditableColumnField="title">
          <p-cellEditor>
            <ng-template #input>
              <input pInputText type="text" [(ngModel)]="rowData.title" />
            </ng-template>
            <ng-template #output> {{ rowData.title }} </ng-template>
          </p-cellEditor>
        </td>
        <td></td>
      </tr>
    </ng-template>
  </p-table>
</p-drawer>

<p-dialog
  [header]="i18nService.t()('AddFile')"
  [modal]="true"
  [(visible)]="fileDialogVisible"
  [style]="{ width: '50vw', height: '90vh' }"
  styleClass="overflow-hidden"
>
  <div class="flex gap-4">
    <input type="text" pInputText [(ngModel)]="searchId" />
    <input type="text" pInputText [(ngModel)]="searchName" />
    <p-datepicker
      [(ngModel)]="searchDate"
      [showIcon]="true"
      [showClear]="true"
      dateFormat="yy-mm-dd"
    ></p-datepicker>
    <p-button label="Reset" (onClick)="resetFilters()"></p-button>
  </div>
  <div class="grid grid-cols-2 gap-4 flex-1 mt-4">
    <p-virtualscroller
      [items]="filterItems()"
      [itemSize]="50"
      styleClass="border h-full"
    >
      <ng-template #item let-item let-options="options">
        <div class="p-2">
          <p-checkbox
            [inputId]="item.value"
            name="group"
            [value]="item"
            [(ngModel)]="selectedItems"
          />
          <label [for]="item.value" class="ml-2"> {{ item.label }} </label>
        </div>
      </ng-template>
    </p-virtualscroller>
    <p-virtualscroller
      [items]="selectedItems()"
      [itemSize]="50"
      styleClass="border h-full"
    >
      <ng-template #item let-item let-options="options">
        <div class="p-2">
          <p-checkbox
            [inputId]="item.value"
            name="group"
            [value]="item"
            [(ngModel)]="selectedItems"
          />
          <label [for]="item.value" class="ml-2"> {{ item.label }} </label>
        </div>
      </ng-template>
    </p-virtualscroller>
  </div>
  <div class="flex justify-end gap-2 mt-4">
    <p-button [label]="i18nService.t()('Cancel')" severity="secondary"></p-button>
    <p-button [label]="i18nService.t()('Save')"></p-button>
  </div>
</p-dialog>
