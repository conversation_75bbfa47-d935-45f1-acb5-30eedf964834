import { DragDropModule } from '@angular/cdk/drag-drop';
import {
  Component,
  computed,
  effect,
  inject,
  signal,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TableModule } from 'primeng/table';
import { TreeTableModule } from 'primeng/treetable';
import { ChannelService } from '@/proxy/holy-bless/channels/channel.service';
import { ChannelDto } from '@/proxy/holy-bless/channels/dtos';
import { CTreeTableComponent } from '../../components/c-tree-table/c-tree-table';
import { cloneDeep } from 'lodash-es';
import { DrawerModule } from 'primeng/drawer';
import { CollectionService } from '@/proxy/holy-bless/collections/collection.service';
import { CheckboxModule } from 'primeng/checkbox';
import {
  ChannelSource,
  channelSourceOptions,
} from '@/proxy/holy-bless/enums/channel-source.enum';
import { EBookService } from '@/proxy/holy-bless/books/ebook.service';
import { AlbumService } from '@/proxy/holy-bless/albums/album.service';
import { VirtualFolderService } from '@/proxy/holy-bless/virtual-folders/virtual-folder.service';
import { map } from 'rxjs';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-channel',
  templateUrl: './channel.html',
  styleUrls: ['./channel.scss'],
  standalone: true,
  imports: [
    TableModule,
    ButtonModule,
    TreeTableModule,
    DragDropModule,
    SelectButtonModule,
    FormsModule,
    InputTextModule,
    DialogModule,
    SelectModule,
    CTreeTableComponent,
    DrawerModule,
    CheckboxModule,
  ],
})
export class ChannelComponent {
  #ChannelService = inject(ChannelService);
  #CollectionService = inject(CollectionService);
  #EBookService = inject(EBookService);
  #VirtualFolderService = inject(VirtualFolderService);
  #AlbumService = inject(AlbumService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);

  // lang
  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');

  channelSourceOptions = computed(() =>
    channelSourceOptions.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  // table
  cols = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID'), width: '8rem'  },
    { field: 'name', header: this.i18nService.t()('Title') },
    { field: 'contentCode', header: this.i18nService.t()('ContentCode') },
    {
      field: 'channelSource',
      header: this.i18nService.t()('ChannelSource'),
      type: 'select',
      selectData: this.channelSourceOptions(),
    },
  ]);
  data = signal<any[]>([]);

  // drawer
  drawerVisible = signal<boolean>(false);
  openChannel = signal<any>(null);
  subData = signal<any[]>([]);
  selectSub = signal<any[]>([]);

  constructor() {
    effect(() => {
      if (!this.drawerVisible()) {
        this.selectSub.set([]);
      }
    });
  }

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#ChannelService.getAllChannels(this.lang()).subscribe({
      next: (res) => {
        this.data.set(res);
        this.#LoadingService.loading.set(false);
      },
      error: () => {
        this.#LoadingService.loading.set(false);
      },
    });
  }

  handleDrop({ channelId, toParentId, beforeId }: any) {
    this.#ChannelService
      .moveChannel(channelId, toParentId, beforeId)
      .subscribe({
        next: (res) => {
          this.loadData();
        },
      });
  }

  selectChannel = signal<any>(null);

  mode = signal<'create' | 'edit'>('create');

  handleEdit(rowData: any) {
    this.selectChannel.set(cloneDeep(rowData));
    this.mode.set('edit');
  }

  handleCreate() {
    this.selectChannel.set({});
    this.mode.set('create');
  }

  handleAdd(rowData: any) {
    this.drawerVisible.set(true);
    this.openChannel.set(rowData);
    let api: any = null;
    if (rowData.channelSource === ChannelSource.Collection) {
      api = this.#CollectionService.getFirstByChannelId(rowData.id);
    } else if (rowData.channelSource === ChannelSource.Ebook) {
      api = this.#EBookService
        .getEBooksByChannelId(rowData.id)
        .pipe(
          map((res) =>
            (res || []).map((item) => ({ ...item, name: item.title })),
          ),
        );
    } else if (rowData.channelSource === ChannelSource.VirtualDisk) {
      api = this.#VirtualFolderService
        .getList({ channelId: rowData.id, maxResultCount: 1000 })
        .pipe(
          map((res) =>
            (res.items || []).map((item) => ({
              ...item,
              name: item.folderName,
            })),
          ),
        );
    } else {
      api = this.#AlbumService
        .getAlbumsByChannel(rowData.id)
        .pipe(
          map((res) =>
            (res || []).map((item) => ({ ...item, name: item.title })),
          ),
        );
    }
    if (!api) return;
    api.subscribe({
      next: (res: any) => {
        if (res) {
          if (Array.isArray(res)) {
            this.subData.set(res);
          } else {
            this.subData.set([res]);
          }
        } else {
          this.subData.set([]);
        }
      },
    });
  }

  handleRemove() {
    if (this.selectSub().length === 0) return;
    const ids = this.selectSub().map((i) => i.id);
    let api: any = null;
    const openChannel = this.openChannel();
    if (openChannel.channelSource === ChannelSource.Collection) {
      api = this.#CollectionService.unlinkFromChannelByCollectionIds(ids);
    } else if (openChannel.channelSource === ChannelSource.Ebook) {
      api = this.#EBookService.unlinkFromChannelByEBookIds(ids);
    } else if (openChannel.channelSource === ChannelSource.VirtualDisk) {
      api = this.#VirtualFolderService.unlinkFromChannelByVirtualFolderIds(ids);
    } else if (openChannel.channelSource === ChannelSource.PodCast) {
      api = this.#AlbumService.unlinkFromChannelByAlbumIds(ids);
    }
    api.subscribe({
      next: (res: any) => {
        this.drawerVisible.set(false);
        this.selectSub.set([]);
        this.loadData();
      },
    });
  }

  handleRowReorder(event: any) {
    const { dropIndex } = event;
    const sourceId = this.subData()[dropIndex].id;
    const targetId = this.subData()[dropIndex + 1]?.id;
    const openChannel = this.openChannel();
    let api = null;
    if (openChannel.channelSource === ChannelSource.Collection) {
      // api = this.#CollectionService.moveCollection(sourceId, targetId);
    } else if (openChannel.channelSource === ChannelSource.Ebook) {
      api = this.#EBookService.moveBook(sourceId, targetId);
    } else if (openChannel.channelSource === ChannelSource.VirtualDisk) {
      // api = this.#VirtualFolderService.moveVirtualFolder(sourceId, targetId);
    } else if (openChannel.channelSource === ChannelSource.PodCast) {
      api = this.#AlbumService.moveAlbum(sourceId, targetId);
    }
    api?.subscribe({
      next: (res: any) => {
        this.handleAdd(this.openChannel());
      },
    });
  }

  updateSelectChannel() {
    if (this.mode() === 'edit') {
      this.#ChannelService
        .update(this.selectChannel().id, this.selectChannel())
        .subscribe({
          next: (res) => {
            this.loadData();
            this.selectChannel.set(null);
          },
        });
    } else {
      this.#ChannelService
        .create({
          ...this.selectChannel(),
          parentChannelId: null,
          languageCode: 'zh-Hans',
          weight: 1,
        })
        .subscribe({
          next: (res) => {
            this.loadData();
            this.selectChannel.set(null);
          },
        });
    }
  }

  handleDelete(rowData: any) {
    this.#ChannelService.delete(rowData.id).subscribe({
      next: (res) => {
        this.loadData();
      },
    });
  }
}
