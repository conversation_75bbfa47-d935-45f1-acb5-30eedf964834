import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { ChannelSource } from '../../enums/channel-source.enum';

export interface ChannelDto extends EntityDto<number> {
  parentChannelId?: number;
  contentCode?: string;
  languageCode?: string;
  name?: string;
  weight: number;
  channelSource?: ChannelSource;
}

export interface ChannelSearchDto extends PagedAndSortedResultRequestDto {
  contentCode?: string;
}

export interface ChannelTreeDto {
  id: number;
  parentChannelId?: number;
  name?: string;
  weight: number;
  contentCode?: string;
  isRoot: boolean;
  channelSource?: ChannelSource;
  children: ChannelTreeDto[];
}

export interface CreateUpdateChannelDto {
  parentChannelId?: number;
  contentCode?: string;
  languageCode?: string;
  name: string;
  weight: number;
  channelSource?: ChannelSource;
}
