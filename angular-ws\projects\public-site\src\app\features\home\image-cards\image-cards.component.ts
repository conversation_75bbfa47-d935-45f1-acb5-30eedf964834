import { Component, HostListener, inject, signal } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ActivatedRoute, Router } from '@angular/router';
import { ArticleSummaryResult } from '@/proxy/holy-bless/results';
import { TranslatePipe } from '@/pipes/translate.pipe';
import { I18nService } from '@/services/i18n.service';
import { skip, Subscription } from 'rxjs';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { MenuItem } from 'primeng/api';
import { LoadingService } from '@/services/loading.service';
import { DatePickerModule } from 'primeng/datepicker';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';

@Component({
  selector: 'app-image-cards',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    PaginatorModule,
    CalendarModule,
    ButtonModule,
    FormsModule,
    BreadcrumbModule,
    DatePickerModule,
  ],
  providers: [DatePipe],
  templateUrl: './image-cards.component.html',
  styleUrls: ['./image-cards.component.scss'],
})
export class ImageCardsComponent {
  home: MenuItem = { icon: 'pi pi-home', routerLink: '/' };
  breadcrumbItems = signal<MenuItem[]>([]);
  // 分页相关属性
  totalRecords = 0;
  rows = 10;
  first = 0;
  isMobile = false;

  selectedDate: Date | null = null;
  collectionId: number | null = null;

  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  i18nService = inject(I18nService);
  #route = inject(ActivatedRoute);
  router = inject(Router);
  subs = new Subscription();
  loadingService = inject(LoadingService);

  cardItems: ArticleSummaryResult[] = [];

  name = '';

  ngOnInit() {
    this.#route.params.subscribe((params) => {
      if (params['collectionId']) {
        this.collectionId = +params['collectionId'];
        this.changeLanguage(this.collectionId);
        this.loadCollectionSummary();
      }
    });
  }

  private initBreadcrumb() {
    const items: MenuItem[] = [
      {
        label: this.name,
      },
    ];

    this.breadcrumbItems.set(items);
  }

  changeLanguage(collectionId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe(() => {
      this.#ReadOnlyCollectionService
        .getLanguageMatchingCollection(collectionId)
        .subscribe({
          next: (collection) => {
            if (!collection) {
              this.router.navigateByUrl(`/landing`);
              return;
            }
            if (collection.id !== collectionId) {
              this.router.navigateByUrl(`/home/<USER>/${collection.id}`);
            }
          },
        });
    });
    this.subs.add(sub);
  }

  private loadCollectionSummary() {
    if (!this.collectionId) return;
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionSummary(this.collectionId, {
        skip: this.first,
        maxResultCount: this.rows,
      })
      .subscribe({
        next: (data) => {
          this.name = data.name || '';
          this.totalRecords = data.totalRecords;
          this.cardItems = data.articles;
          this.initBreadcrumb();
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取摘要数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  navigateToArticle(item: ArticleSummaryResult) {
    if (!item.id) return;
    this.router.navigateByUrl(`/home/<USER>/${item.id}`);
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    this.loadCollectionSummary();
  }

  onDateChange(event: Date | null) {
    this.selectedDate = event;
    this.loadCollectionSummary();
  }

  constructor() {
    this.checkMobile();
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10];
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  private checkMobile() {
    this.isMobile = window.innerWidth <= 768;
  }

  // 播放本页
  playCurrentPage() {}

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
