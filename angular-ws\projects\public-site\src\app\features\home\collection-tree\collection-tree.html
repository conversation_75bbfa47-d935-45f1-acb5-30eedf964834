<div class="flex flex-1 flex-col md:flex-row">
  @if((mobileService.isMobile | async)) {
  <app-mobile-catagory-files-drawer
    [leftTemplate]="treeTemplate"
    [showRightIcon]="false"
    [leftDrawerVisible]="leftDrawerVisible"
  ></app-mobile-catagory-files-drawer>
  } @else {
  <ng-container *ngTemplateOutlet="treeTemplate"></ng-container>
  }
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1 overflow-y-auto"
    style="height: calc(100vh - 5rem)"
  >
    <p-accordion value="0">
      @for (item of items; track $index) {
      <p-accordion-panel [value]="item.id">
        <p-accordion-header>
          <p class="flex-1 flex justify-between items-center">
            <a name="_Toc{{ item.id }}">{{ item.title }}</a>
            <p-button
              icon="pi pi-play-circle"
              [text]="true"
              (click)="onPlayClick(item)"
            />
          </p>
        </p-accordion-header>
        <p-accordion-content>
          <p class="m-0 main-content-text" [innerHTML]="item.content"></p>
        </p-accordion-content>
      </p-accordion-panel>
      }
    </p-accordion>
  </div>
  @if(!(mobileService.isMobile | async)) {
  <div class="p-6 w-[20rem] border-l-2">
    <ng-container *ngTemplateOutlet="catalogTemplate"></ng-container>
  </div>
  }
</div>

@if((mobileService.isMobile | async)) {
<app-catalog
  [catalogList]="items"
  (onItemClick)="onTocItemClick($event)"
></app-catalog>
}

<ng-template #treeTemplate>
  <p-tree
    [value]="files"
    [styleClass]="`${(mobileService.isMobile | async) ? 'w-full' : 'w-[20rem]'} h-full`"
    selectionMode="single"
    [(selection)]="selectedFile"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    (onNodeSelect)="loadArticleSummary($event.node)"
  />
</ng-template>

<ng-template #catalogTemplate>
  <div>
    <div class="flex flex-col max-h-[80vh] overflow-y-auto">
      <h3 class="text-lg font-semibold mb-4">目录</h3>

      <!-- 如果没有目录项，显示提示 -->
      <div *ngIf="items.length === 0" class="text-sm italic">暂无目录</div>

      <!-- 动态生成的目录项 -->
      <a
        *ngFor="let item of items;"
        class="mt-2 cursor-pointer hover:text-primary-600 underline"
        (click)="onTocItemClick(item)"
      >
        {{ item.title }}
      </a>
    </div>
  </div>
</ng-template>
