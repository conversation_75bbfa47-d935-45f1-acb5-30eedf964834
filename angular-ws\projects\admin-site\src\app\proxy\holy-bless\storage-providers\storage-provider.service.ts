import type { CreateUpdateStorageProviderDto, StorageBucketDto, StorageProviderDto } from './dtos/models';
import type { StorageProvider } from './models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class StorageProviderService {
  apiName = 'Default';
  

  create = (input: CreateUpdateStorageProviderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageProviderDto>({
      method: 'POST',
      url: '/api/app/storage-provider',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/storage-provider/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageProviderDto>({
      method: 'GET',
      url: `/api/app/storage-provider/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getCloudFlareBuckets = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto[]>({
      method: 'GET',
      url: '/api/app/storage-provider/cloud-flare-buckets',
    },
    { apiName: this.apiName,...config });
  

  getCloudFlareProviders = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageProvider[]>({
      method: 'GET',
      url: '/api/app/storage-provider/cloud-flare-providers',
    },
    { apiName: this.apiName,...config });
  

  getFileAccessUrlSyncByFileNameAndRelativePathAndPreferCountry = (fileName: string, relativePath: string, preferCountry: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'GET',
      responseType: 'text',
      url: '/api/app/storage-provider/file-access-url-sync',
      params: { fileName, relativePath, preferCountry },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<StorageProviderDto>>({
      method: 'GET',
      url: '/api/app/storage-provider',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getProvidersByCountryByPreferCountry = (preferCountry: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageProviderDto>({
      method: 'GET',
      url: '/api/app/storage-provider/providers-by-country',
      params: { preferCountry },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateStorageProviderDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageProviderDto>({
      method: 'PUT',
      url: `/api/app/storage-provider/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
