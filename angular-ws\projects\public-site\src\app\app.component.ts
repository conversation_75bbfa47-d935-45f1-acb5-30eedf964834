import { Component, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NavigationMenuComponent } from './components/navigation-menu/navigation-menu.component';
import { DrawerService } from './services/drawer.service';
import { PlayerDrawerComponent } from './components/player-drawer/player-drawer';
import { SettingDrawerComponent } from './components/setting-drawer/setting-drawer';
import { BlockUIModule } from 'primeng/blockui';
import { LoadingService } from './services/loading.service';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { I18nService } from './services/i18n.service';
import { DownloadProgressComponent } from './components/download-progress/download-progress';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    CommonModule,
    NavigationMenuComponent,
    SettingDrawerComponent,
    PlayerDrawerComponent,
    BlockUIModule,
    ProgressSpinnerModule,
    DownloadProgressComponent
  ],
  templateUrl: './app.html',
  styleUrls: ['./app.scss']
})
export class AppComponent {
  title = 'Holybless';

  drawerService = inject(DrawerService);
  loadingService = inject(LoadingService);
  i18nService = inject(I18nService);

}
