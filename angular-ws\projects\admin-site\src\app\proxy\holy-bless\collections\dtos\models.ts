import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { ArticleTitleDto } from '../../articles/dtos/models';
import type { BucketFileDto } from '../../buckets/dtos/models';
import type { ListStyle } from '../../enums/list-style.enum';
import type { DefaultOrderByField } from '../../enums/default-order-by-field.enum';

export interface CollectionArticleDto {
  articleId: number;
  weight?: number;
}

export interface CollectionArticleSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
  status?: PublishStatus;
}

export interface CollectionArticleTreeDto {
  id: number;
  name?: string;
  contentCode?: string;
  parentCollectionId?: number;
  description?: string;
  status?: PublishStatus;
  isRoot: boolean;
  children: CollectionArticleTreeDto[];
  articles: ArticleTitleDto[];
}

export interface CollectionDto extends EntityDto<number> {
  parentCollectionId?: number;
  contentCode?: string;
  languageCode?: string;
  thumbnailBucketFile: BucketFileDto;
  name?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  listStyle?: ListStyle;
  status?: PublishStatus;
  channelId?: number;
  channelName?: string;
  memo?: string;
  defaultOrderBy?: DefaultOrderByField;
}

export interface CollectionFileSearchDto extends PagedAndSortedResultRequestDto {
  collectionId: number;
}

export interface CollectionSearchDto extends PagedAndSortedResultRequestDto {
  status?: PublishStatus;
  channelId?: number;
}

export interface CollectionToArticleDetailDto {
  collectionId: number;
  collectionName?: string;
  articleId: number;
  articleTitle?: string;
  weight: number;
}

export interface CollectionToArticleKey {
  collectionId: number;
  articleId: number;
}

export interface CollectionToFileDto {
  fileId: number;
  weight: number;
}

export interface CollectionTreeDto {
  id: number;
  name?: string;
  contentCode?: string;
  parentCollectionId?: number;
  description?: string;
  status?: PublishStatus;
  isRoot: boolean;
  children: CollectionTreeDto[];
}

export interface CreateUpdateCollectionDto {
  parentCollectionId?: number;
  contentCode?: string;
  languageCode?: string;
  name: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  listStyle?: ListStyle;
  status?: PublishStatus;
  memo?: string;
  defaultOrderBy?: DefaultOrderByField;
}

export interface CreateUpdateCollectionToArticleDto {
  collectionId: number;
  articleId: number;
  weight?: number;
}
