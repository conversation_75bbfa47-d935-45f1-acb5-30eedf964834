import type { AuditedEntityDto, EntityDto } from '@abp/ng.core';

export interface ChapterDto extends AuditedEntityDto<number> {
  eBookId: number;
  parentChapterId?: number;
  title?: string;
  weight: number;
  views: number;
  content?: string;
  contentAudioFileId?: number;
}

export interface ChapterToArticleDto extends EntityDto {
  chapterId: number;
  articleId: number;
  weight: number;
  chapterTitle?: string;
  articleTitle?: string;
}

export interface ChapterTreeDto {
  id: number;
  eBookId: number;
  parentChapterId?: number;
  title?: string;
  content?: string;
  weight: number;
  views: number;
  isRoot: boolean;
  articleCount: number;
  children: ChapterTreeDto[];
}

export interface CreateUpdateChapterDto {
  eBookId: number;
  parentChapterId?: number;
  title: string;
  weight: number;
  views: number;
  content?: string;
}

export interface CreateUpdateChapterToArticleDto {
  chapterId: number;
  articleId: number;
  weight: number;
}

export interface CreateUpdateEBookDto {
  title: string;
  description?: string;
  weight: number;
  channelId?: number;
  thumbnailFileId?: number;
  views: number;
  likes: number;
  languageCode?: string;
}

export interface EBookDto extends AuditedEntityDto<number> {
  title?: string;
  description?: string;
  weight: number;
  channelId?: number;
  channelName?: string;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  views: number;
  likes: number;
  languageCode?: string;
}
