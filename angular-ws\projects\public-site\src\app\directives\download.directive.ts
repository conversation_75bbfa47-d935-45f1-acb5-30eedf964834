import { VideoCacheService } from '@/services/video-cache.service';
import { Directive, HostListener, inject, Input } from '@angular/core';

@Directive({
  selector: '[appDownload]',
})
export class DownloadDirective {
  @Input('appDownload') downloadUrl!: string;
  @Input() downloadName?: string;

  #VideoCacheService = inject(VideoCacheService);

  @HostListener('click')
  async onClick() {
    if (!this.downloadUrl) return;
    const cached = await this.#VideoCacheService.getCachedVideo(
      this.downloadUrl,
    );
    let downloadUrl = this.downloadUrl;
    if (cached) {
      downloadUrl = cached.blob
        ? URL.createObjectURL(cached.blob)
        : this.downloadUrl;
    }
    try {
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = this.downloadName || this.extractFileName() || 'download';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      if (!cached) {
        await this.#VideoCacheService.smartCacheVideo(downloadUrl, a.download);
      }
    } catch (e) {
      console.error('Download failed:', e);
      // 如果创建下载链接失败，直接打开URL
      window.open(downloadUrl, '_blank');
    }
  }

  private extractFileName(): string {
    try {
      const url = new URL(this.downloadUrl);
      const pathname = url.pathname;
      const fileName = pathname.split('/').pop();
      return fileName || 'download';
    } catch {
      return this.downloadUrl.split('/').pop() || 'download';
    }
  }
}
