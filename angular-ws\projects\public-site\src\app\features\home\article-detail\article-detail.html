<div class="flex flex-col md:flex-row flex-1">
  @if(mobileService.isMobile | async) {
  <app-mobile-catagory-files-drawer
    [rightTemplate]="galleriaTemplate"
    [showLeftIcon]="false"
    [showRightIcon]="notImageArticleFiles().length > 0 || imageArticleFiles().length > 0 || primaryArticleFiles().length > 0"
  />
  }

  <div class="flex flex-col md:!flex-row flex-1">
    <div class="articaldetail-container prose max-w-none p-6 flex-1">
      <h1 class="text-3xl font-bold mb-4">{{articleDetail()?.title}}</h1>
      <p class="text-sm mb-4 flex items-center gap-2">
        @if (articleDetail()) {
        <i class="pi pi-clock"></i>
        {{ articleDetail()?.deliveryDate | date: 'yyyy-MM-dd' }} }
      </p>
      <div
        class="mb-4 main-content-text"
        [innerHTML]="articleDetail()?.content"
      ></div>
    </div>
    <div class="p-6 max-w-[20rem]">
      @if(!(mobileService.isMobile | async)) {
      <ng-container *ngTemplateOutlet="galleriaTemplate"></ng-container>
      }
    </div>
  </div>
</div>

<ng-template #galleriaTemplate>
  <div>
    <div>
      <p-galleria
        [value]="imageArticleFiles()"
        indicatorsPosition="right"
        [showIndicators]="true"
        [showThumbnails]="false"
        [showIndicatorsOnItem]="true"
        [containerStyle]="{ 'width': '100%','margin-top': '2em' }"
      >
        <ng-template pTemplate="item" let-item>
          <img
            [src]="item.fileUrl"
            class="max-w-full max-h-full w-auto h-auto rounded-lg object-cover shadow-lg"
          />
        </ng-template>
      </p-galleria>
    </div>
    <app-play-download
      [primaryArticleFiles]="primaryArticleFiles()"
      [notImageArticleFiles]="notImageArticleFiles()"
    ></app-play-download>
  </div>
</ng-template>
