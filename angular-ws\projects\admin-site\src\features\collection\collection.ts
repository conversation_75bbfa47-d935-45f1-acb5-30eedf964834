import { Component, inject, signal, computed } from '@angular/core';
import { CTreeTableComponent } from '../../components/c-tree-table/c-tree-table';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { CollectionService } from '@/proxy/holy-bless/collections/collection.service';
import { SelectButtonModule } from 'primeng/selectbutton';
import { InputTextModule } from 'primeng/inputtext';
import { listStyleOptions } from '@/proxy/holy-bless/enums/list-style.enum';
import { SelectModule } from 'primeng/select';
import { ChannelService } from '@/proxy/holy-bless/channels/channel.service';
import { TreeSelectModule } from 'primeng/treeselect';
import { cloneDeep } from 'lodash-es';
import { DrawerModule } from 'primeng/drawer';
import { ArticleService } from '@/proxy/holy-bless/articles/article.service';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { CheckboxModule } from 'primeng/checkbox';
import { ScrollerModule } from 'primeng/scroller';
import { TableModule } from 'primeng/table';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-collection',
  standalone: true,
  templateUrl: './collection.html',
  styleUrls: ['./collection.scss'],
  imports: [
    CTreeTableComponent,
    ButtonModule,
    DialogModule,
    FormsModule,
    SelectButtonModule,
    InputTextModule,
    SelectModule,
    TreeSelectModule,
    DrawerModule,
    CheckboxModule,
    ScrollerModule,
    TableModule,
  ],
})
export class CollectionComponent {
  #CollectionService = inject(CollectionService);
  #ChannelService = inject(ChannelService);
  #ArticleService = inject(ArticleService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);

  // lang
  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');

  // table
  data = signal<any[]>([]);
  cols = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID'), width: '8rem' },
    { field: 'name', header: this.i18nService.t()('Name') },
    { field: 'channelName', header: this.i18nService.t()('Channel') },
    { field: 'contentCode', header: this.i18nService.t()('ContentCode') },
  ]);
  expandAll = signal(false);

  // edit dialog
  mode = signal<'edit' | 'create'>('create');
  selectData = signal<any>(null);

  collectionTree = signal<any[]>([]);
  collectionMap = new Map();

  channelTree = signal<any[]>([]);
  channelMap = new Map();
  listStyleOptions = listStyleOptions;

  // drawer
  drawerVisible = signal<boolean>(false);
  subData = signal<ArticleAggregateResult[]>([]);
  selectSub = signal<ArticleAggregateResult[]>([]);

  ngOnInit() {
    this.loadData();
    this.loadChannelData();
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#CollectionService.getAllCollections(this.lang()).subscribe({
      next: (res) => {
        this.data.set(res || []);
        this.collectionTree.set(
          this.buildTreeData(
            res || [],
            'parentCollectionId',
            undefined,
            undefined,
            this.collectionMap,
          ),
        );
        this.#LoadingService.loading.set(false);
      },
      error: () => {
        this.#LoadingService.loading.set(false);
      },
    });
  }

  loadChannelData() {
    this.#ChannelService.getAllChannels(this.lang()).subscribe({
      next: (res) => {
        this.channelTree.set(
          this.buildTreeData(
            res,
            undefined,
            undefined,
            undefined,
            this.channelMap,
          ),
        );
      },
    });
  }

  buildTreeData(
    flatData: any[],
    parentKey = 'parentChannelId',
    key = 'id',
    label = 'name',
    map = new Map(),
  ) {
    const result: any[] = [];

    flatData.forEach((item) => {
      map.set(item[key], {
        ...item,
        key: item[key],
        label: item[label],
        children: [],
      });
    });

    flatData.forEach((item) => {
      const node = map.get(item[key]);

      if (!item[parentKey]) {
        result.push(node);
      } else {
        const parent = map.get(item[parentKey]);
        if (parent) {
          parent.children.push(node);
        }
      }
    });
    return result;
  }

  handleDrop(data: any) {
    this.#CollectionService
      .moveCollection(data.channelId, data.toParentId, data.beforeId)
      .subscribe({
        next: () => {
          this.loadData();
        },
      });
  }

  handleAdd(rowData: any) {
    this.drawerVisible.set(true);
    this.#ArticleService
      .getArticleAggregatesByCollectionId(rowData.id)
      .subscribe({
        next: (res) => {
          this.subData.set(res);
        },
      });
  }

  handleCreate() {
    this.mode.set('create');
    this.selectData.set({});
  }

  handleEdit(rowData: any) {
    this.mode.set('edit');
    const _rowData = cloneDeep(rowData);
    _rowData.parentCollection = null;
    if (_rowData.parentCollectionId) {
      _rowData.parentCollection = {
        key: _rowData.parentCollectionId,
        label: this.collectionMap.get(_rowData.parentCollectionId)?.name,
      };
    }
    _rowData.channel = null;
    if (_rowData.channelId) {
      _rowData.channel = {
        key: _rowData.channelId,
        label: this.channelMap.get(_rowData.channelId)?.name,
      };
    }
    this.selectData.set(_rowData);
  }

  updateSelectChannel() {
    const data = cloneDeep(this.selectData());
    if (data.parentCollection) {
      data.parentCollectionId = data.parentCollection.key;
    }
    if (data.channel) {
      data.channelId = data.channel.key;
    }
    delete data.parentCollection;
    delete data.channel;
    if (this.mode() === 'create') {
      this.#CollectionService
        .create({ ...data, languageCode: this.lang() })
        .subscribe({
          next: () => {
            this.loadData();
            this.selectData.set(null);
          },
        });
    } else {
      this.#CollectionService.update(data.id, data).subscribe({
        next: () => {
          this.loadData();
          this.selectData.set(null);
        },
      });
    }
  }
  handleDelete(rowData: any) {
    this.#CollectionService.delete(rowData.id).subscribe({
      next: () => {
        this.loadData();
      },
    });
  }

  handleRemove() {
    // const selectSub = this.selectSub();
  }

  toggleAll() { 
    this.expandAll.set(!this.expandAll());
  }
}
