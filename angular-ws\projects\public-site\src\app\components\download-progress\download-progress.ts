import { DownloadService } from "@/services/download.service";
import { Component, inject } from "@angular/core";
import { ProgressBarModule } from "primeng/progressbar";

@Component({
  selector: 'app-download-progress',
  standalone: true,
  templateUrl: './download-progress.html',
  styleUrls: ['./download-progress.scss'],
  imports: [
    ProgressBarModule
  ]
})
export class DownloadProgressComponent {
  downloadService = inject(DownloadService);
}
