<div
  class="fixed bottom-20 right-0 translate-x-6 opacity-30 rounded-full bg-[#555] shadow-lg p-4 flex items-center justify-center text-[#fff] transition"
  [ngClass]="{'!-translate-x-4 opacity-100': show()}"
  (click)="btnClick()"
>
  <i class="pi pi-bars text-2xl"></i>
</div>

<p-drawer [(visible)]="visible" position="bottom" [showCloseIcon]="false" [style]="{
  height: '50vh',
}">
<ul>
  @for (item of catalogList; track $index) {
    <li class="flex justify-center py-2 text-lg text-center" (click)="itemClick(item)">{{ item.label || item.text || item.title }}</li>
  }
</ul>
</p-drawer>
