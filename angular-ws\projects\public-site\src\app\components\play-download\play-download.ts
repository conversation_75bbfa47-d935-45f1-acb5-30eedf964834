import { DownloadDirective } from '@/directives/download.directive';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { I18nService } from '@/services/i18n.service';
import { PlayerService } from '@/services/player.service';
import { Component, inject, Input } from '@angular/core';

@Component({
  selector: 'app-play-download',
  standalone: true,
  imports: [RemoveExtensionPipe, DownloadDirective],
  templateUrl: './play-download.html',
  styleUrls: ['./play-download.scss'],
})
export class PlayDownloadComponent {
  @Input() primaryArticleFiles: any[] = [];
  @Input() notImageArticleFiles: any[] = [];

  i18nService = inject(I18nService);
  playerService = inject(PlayerService);

  playMedia(item: any) {
    this.playerService.playVideo(item);
  }
}
