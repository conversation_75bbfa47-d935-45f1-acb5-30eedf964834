import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import zhHans from '@angular/common/locales/zh-Hans';
import en from '@angular/common/locales/en';
import { LOCALE_ID } from '@angular/core';

registerLocaleData(zh, 'zh');
registerLocaleData(zhHans, 'zh-Hans');
registerLocaleData(en, 'en');

bootstrapApplication(AppComponent, {
  ...appConfig,
  providers: [
    ...(appConfig.providers ?? []),
    { provide: LOCALE_ID, useValue: 'zh-Hans' }, // 设置默认语言环境
  ],
}).catch((err) => console.error(err));
