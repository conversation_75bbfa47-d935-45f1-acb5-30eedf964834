<div class="flex flex-1 flex-col md:flex-row">
  @if((mobileService.isMobile | async)) {
  <div class="p-6">
    <ng-container *ngTemplateOutlet="titleImgTemplate"></ng-container>
  </div>
  }
  <div class="articaldetail-container prose max-w-none p-6 flex-1">
    <div class="flex items-center gap-2">
      <p-button
        icon="pi pi-play-circle"
        [label]="i18nService.translate('play')"
        [outlined]="true"
        (onClick)="playerService.multiPlayVideo(selectedProducts)"
      />
    </div>
    <p-table
      [value]="products()"
      [virtualScroll]="!(mobileService.isMobile | async)"
      [scrollHeight]="!(mobileService.isMobile | async) ? '32rem' : 'auto'"
      size="small"
      stripedRows
      [(selection)]="selectedProducts"
    >
      <ng-template #header>
        <tr>
          <th style="width: 2rem"><p-tableHeaderCheckbox /></th>
          <th style="width: 2rem"></th>
          <th>{{ i18nService.translate("name") }}</th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template #body let-product let-index="rowIndex">
        <tr (click)="selectedNode.set(product)">
          <td>
            <p-tableCheckbox [value]="product" />
          </td>
          <td>{{ index+1 }}</td>
          <td>{{ product.title || (product.fileName | removeExtension) }}</td>
          <td>
            <div class="flex items-center gap-2">
              <p-button
                icon="pi pi-play-circle"
                styleClass="!rounded-full"
                [text]="true"
                (onClick)="videoPlay(product)"
              />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template #footer> </ng-template>
    </p-table>
  </div>
  @if(!(mobileService.isMobile | async)) {
  <div class="p-6 max-w-[20rem]">
    <ng-container *ngTemplateOutlet="titleImgTemplate"></ng-container>
  </div>
  }
</div>

<ng-template #titleImgTemplate>
  <div>
    <img
      [src]="albumDetail()?.thumbnailUrl"
      class="w-full"
      alt="Album Cover"
    />
    <div>
      <div class="mt-6">
        <h1 class="text-xl font-bold">{{ albumDetail()?.title }}</h1>
      </div>
    </div>
  </div>
</ng-template>
