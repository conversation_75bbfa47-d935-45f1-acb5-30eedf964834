import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class S3StorageManagerService {
  apiName = 'Default';
  

  downloadBucketFileByBucketIdAndFileNamePath = (bucketId: number, fileNamePath: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, number[]>({
      method: 'GET',
      url: '/DownloadBucketFile',
      params: { bucketId, fileNamePath },
    },
    { apiName: this.apiName,...config });
  

  indexCloudFlareFiles = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, number>({
      method: 'POST',
      url: '/api/app/s3Storage-manager/index-cloud-flare-files',
    },
    { apiName: this.apiName,...config });
  

  listFiles = (bucketId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string[]>({
      method: 'GET',
      url: `/api/app/s3Storage-manager/list-files/${bucketId}`,
    },
    { apiName: this.apiName,...config });
  

  listSubfolders = (bucketId: number, prefix?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string[]>({
      method: 'GET',
      url: `/api/app/s3Storage-manager/list-subfolders/${bucketId}`,
      params: { prefix },
    },
    { apiName: this.apiName,...config });
  

  migrateVirtualFolders = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, number>({
      method: 'POST',
      url: '/api/app/s3Storage-manager/migrate-virtual-folders',
    },
    { apiName: this.apiName,...config });
  

  updateFileUrls = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, number>({
      method: 'POST',
      url: '/api/app/s3Storage-manager/update-file-urls',
    },
    { apiName: this.apiName,...config });
  

  uploadFile = (bucketId: number, subFolder: string, fileName: string, fileStream: any, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'POST',
      url: `/api/app/s3Storage-manager/upload-file/${bucketId}`,
      params: { subFolder, fileName },
      body: fileStream,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
