/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "moduleResolution": "bundler",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "paths": {
      "@/*": [
        "projects/public-site/src/app/*",
        "projects/admin-site/src/app/*"
      ],
     "@shared-theme/*": [
        "projects/shared-theme/layout/*"
      ],
      "@proxy-public": ["projects/public-site/src/app/proxy/index.ts"],
      "@proxy-public/*": ["projects/public-site/src/app/proxy/*"],
      "@proxy-admin": ["projects/admin-site/src/app/proxy/index.ts"],
      "@proxy-admin/*": ["projects/admin-site/src/app/proxy/*"],
      "@shared-lib/*":["projects/shared-lib/*"],
      "@shared-layout/*": ["projects/shared-lib/layout/*"],
      "@shared-services/*": ["projects/shared-lib/services/*"]
      
     },
    "useDefineForClassFields": false
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
