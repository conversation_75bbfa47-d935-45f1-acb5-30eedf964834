<p-treetable
  #tt
  [value]="data()"
  [columns]="cols"
  dataKey="key"
  [scrollable]="true"
  [tableStyle]="{ 'min-width': '50rem' }"
  filterMode="strict"
  (onNodeExpand)="expand($event.node)"
  (onNodeCollapse)="collapse($event.node)"
>
  <ng-template #header let-columns>
    <tr>
      <th class="w-[4rem]"></th>
      @for (col of columns; track $index) {
      <th [ngStyle]="{ width: col.width }">{{ col.header }}</th>
      }
      <th></th>
    </tr>
    <tr>
      <th class="w-[4rem]"></th>
      @for (col of columns; track $index) {
      <th>
        <input
          class="w-full"
          pInputText
          type="text"
          (input)="onFilter($event, col.field, col.filterMatchMode)"
        />
      </th>
      }
      <th></th>
    </tr>
  </ng-template>
  <ng-template #body let-rowNode let-rowData="rowData" let-columns="columns">
    <tr
      [ttRow]="rowNode"
      (dragover)="handleDragOver($event, rowData)"
      (drop)="handleDrop($event)"
      (dragenter)="handleDragEnter($event, rowData)"
      (dragleave)="handleDragLeave($event)"
      [class.drag-over-top]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'top'"
      [class.drag-over-middle]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'middle'"
      [class.drag-over-bottom]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'bottom'"
    >
      <td class="w-[4rem]">
        <span
          class="cursor-move"
          draggable="true"
          (dragstart)="handleDragStart($event, rowData)"
        >
          <svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
            <path
              d="M2 4h2v2H2V4zm0 4h2v2H2V8zm0 4h2v2H2v-2zm4-8h2v2H6V4zm0 4h2v2H6V8zm0 4h2v2H6v-2zm4-8h2v2h-2V4zm0 4h2v2h-2V8zm0 4h2v2h-2v-2z"
            />
          </svg>
        </span>
      </td>
      @for (col of columns; track $index) {
      <td [ngStyle]="{ width: col.width }">
        @if($index===0) {
        <p-treetable-toggler [rowNode]="rowNode" />
        <span
          [class.drag-over-top]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'top'"
          [class.drag-over-middle]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'middle'"
          [class.drag-over-bottom]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'bottom'"
        >
          {{ rowData[col.field] | tableDisplay: {type: col.type, selectData:
          col.selectData} }}
        </span>
        } @else{ {{ rowData[col.field] | tableDisplay: {type: col.type,
        selectData: col.selectData} }}}
      </td>
      }
      <td>
        <p-button
          icon="pi pi-pencil"
          [text]="true"
          styleClass="!rounded-full"
          (onClick)="onEdit.emit(rowData)"
        ></p-button>
        <p-button
          icon="pi pi-folder"
          [text]="true"
          styleClass="!rounded-full"
          (onClick)="onAdd.emit(rowData)"
        ></p-button>
        <p-button
          icon="pi pi-trash"
          [text]="true"
          severity="danger"
          styleClass="!rounded-full"
          (onClick)="handleDelete($event, rowData)"
        ></p-button>
      </td>
    </tr>
  </ng-template>
</p-treetable>

<p-confirmpopup />
