import type { LanguageAuditedAggregateRoot } from '../entities/models';
import type { ContentCategory } from '../enums/content-category.enum';
import type { FullAuditedAggregateRoot } from '../../volo/abp/domain/entities/auditing/models';
import type { Country } from '../lookups/models';

export interface StorageBucket extends LanguageAuditedAggregateRoot<number> {
  storageProviderId: number;
  storageProvider: StorageProvider;
  bucketName?: string;
  description?: string;
  spokenLangCode?: string;
  subDomain?: string;
  contentType?: ContentCategory;
}

export interface StorageProvider extends FullAuditedAggregateRoot<number> {
  providerName?: string;
  providerCode?: string;
  preferCountries: Country[];
  environment?: string;
  description?: string;
  bindedDomain?: string;
  buckets: StorageBucket[];
}
