import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ChannelIdContentCodeService {
  private channelIdContentCodeMap: { [key: number]: string } = {};

  constructor() {
    const storedMap = localStorage.getItem('channelIdContentCodeMap');
    if (storedMap) {
      this.channelIdContentCodeMap = JSON.parse(storedMap);
    }
  }

  setChannelIdContentCode(channelId: number, contentCode: string): void {
    this.channelIdContentCodeMap[channelId] = contentCode;
    localStorage.setItem(
      'channelIdContentCodeMap',
      JSON.stringify(this.channelIdContentCodeMap),
    );
  }

  getChannelIdContentCode(channelId: number): string | undefined {
    return this.channelIdContentCodeMap[channelId] || undefined;
  }

  clearChannelIdContentCode(channelId: number): void {
    delete this.channelIdContentCodeMap[channelId];
  }
}
