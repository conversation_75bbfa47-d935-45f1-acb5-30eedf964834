import type { CreateUpdateProviderSecretDto, ProviderSecretDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ProviderSecretService {
  apiName = 'Default';
  

  create = (input: CreateUpdateProviderSecretDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ProviderSecretDto>({
      method: 'POST',
      url: '/api/app/provider-secret',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/provider-secret/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ProviderSecretDto>({
      method: 'GET',
      url: `/api/app/provider-secret/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ProviderSecretDto>>({
      method: 'GET',
      url: '/api/app/provider-secret',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateProviderSecretDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ProviderSecretDto>({
      method: 'PUT',
      url: `/api/app/provider-secret/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
