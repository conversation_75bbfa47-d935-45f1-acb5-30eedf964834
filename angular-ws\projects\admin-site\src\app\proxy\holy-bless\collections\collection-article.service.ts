import type { CollectionArticleDto, CollectionToArticleDetailDto, CollectionToArticleKey, CreateUpdateCollectionToArticleDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CollectionArticleService {
  apiName = 'Default';
  

  addArticlesToCollectionByCollectionIdAndArticleIds = (collectionId: number, articleIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/collection-article/articles-to-collection/${collectionId}`,
      body: articleIds,
    },
    { apiName: this.apiName,...config });
  

  addArticlesToCollectionWithWeightByCollectionIdAndArticles = (collectionId: number, articles: CollectionArticleDto[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/collection-article/articles-to-collection-with-weight/${collectionId}`,
      body: articles,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateUpdateCollectionToArticleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionToArticleDetailDto>({
      method: 'POST',
      url: '/api/app/collection-article',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: CollectionToArticleKey, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/collection-article/${id.collectionId}/${id.articleId}`,
    },
    { apiName: this.apiName,...config });
  

  exists = (collectionId: number, articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, boolean>({
      method: 'POST',
      url: '/api/app/collection-article/exists',
      params: { collectionId, articleId },
    },
    { apiName: this.apiName,...config });
  

  get = (id: CollectionToArticleKey, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionToArticleDetailDto>({
      method: 'GET',
      url: `/api/app/collection-article/${id.collectionId}/${id.articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getByArticleId = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionToArticleDetailDto[]>({
      method: 'GET',
      url: `/api/app/collection-article/by-article-id/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getByCollectionId = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionToArticleDetailDto[]>({
      method: 'GET',
      url: `/api/app/collection-article/by-collection-id/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  removeArticlesFromCollectionByCollectionIdAndArticleIds = (collectionId: number, articleIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/collection-article/articles-from-collection/${collectionId}`,
      params: { articleIds },
    },
    { apiName: this.apiName,...config });
  

  updateWeight = (collectionId: number, articleId: number, weight: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionToArticleDetailDto>({
      method: 'PUT',
      url: '/api/app/collection-article/weight',
      params: { collectionId, articleId, weight },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
