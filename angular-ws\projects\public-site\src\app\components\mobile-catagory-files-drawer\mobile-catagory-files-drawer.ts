import { MobileService } from '@/services/mobile.service';
import { CommonModule } from '@angular/common';
import { Component, effect, inject, Input, OnChanges, signal, SimpleChanges, TemplateRef } from '@angular/core';
import { DrawerModule } from 'primeng/drawer';

@Component({
  selector: 'app-mobile-catagory-files-drawer',
  standalone: true,
  templateUrl: './mobile-catagory-files-drawer.html',
  styleUrls: ['./mobile-catagory-files-drawer.scss'],
  imports: [CommonModule, DrawerModule],
})
export class MobileCategoryFilesDrawerComponent {
  @Input() leftTemplate: TemplateRef<any> | null = null;
  @Input() rightTemplate: TemplateRef<any> | null = null;
  @Input() showLeftIcon: boolean = true;
  @Input() showRightIcon: boolean = true;
  @Input() leftDrawerVisible = signal(false);
  @Input() rightDrawerVisible = signal(false);

  mobileService = inject(MobileService);
  
  onLeftDrawerShow() {
    this.leftDrawerVisible.set(true);
  }

  onRightDrawerShow() {
    this.rightDrawerVisible.set(true);
  }
}
