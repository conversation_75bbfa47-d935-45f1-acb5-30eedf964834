import type { ChapterDto, ChapterToArticleDto, ChapterTreeDto, CreateUpdateChapterDto, CreateUpdateChapterToArticleDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ChapterService {
  apiName = 'Default';
  

  addArticleToChapter = (input: CreateUpdateChapterToArticleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterToArticleDto>({
      method: 'POST',
      url: '/api/app/chapter/article-to-chapter',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateUpdateChapterDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterDto>({
      method: 'POST',
      url: '/api/app/chapter',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/chapter/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterDto>({
      method: 'GET',
      url: `/api/app/chapter/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getChapterArticles = (chapterId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterToArticleDto[]>({
      method: 'GET',
      url: `/api/app/chapter/chapter-articles/${chapterId}`,
    },
    { apiName: this.apiName,...config });
  

  getChapterTreeByEBookId = (eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterTreeDto[]>({
      method: 'GET',
      url: `/api/app/chapter/chapter-tree-by-eBook-id/${eBookId}`,
    },
    { apiName: this.apiName,...config });
  

  getChaptersByEBookId = (eBookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterDto[]>({
      method: 'GET',
      url: `/api/app/chapter/chapters-by-eBook-id/${eBookId}`,
    },
    { apiName: this.apiName,...config });
  

  getChildChapters = (parentChapterId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterDto[]>({
      method: 'GET',
      url: `/api/app/chapter/child-chapters/${parentChapterId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ChapterDto>>({
      method: 'GET',
      url: '/api/app/chapter',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  moveChapter = (chapterId: number, toParentId: number, beforeId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/chapter/move-chapter',
      params: { chapterId, toParentId, beforeId },
    },
    { apiName: this.apiName,...config });
  

  removeArticleFromChapter = (chapterId: number, articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: '/api/app/chapter/article-from-chapter',
      params: { chapterId, articleId },
    },
    { apiName: this.apiName,...config });
  

  removeArticlesFromChapter = (chapterId: number, articleIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/chapter/articles-from-chapter/${chapterId}`,
      params: { articleIds },
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateChapterDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChapterDto>({
      method: 'PUT',
      url: `/api/app/chapter/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
