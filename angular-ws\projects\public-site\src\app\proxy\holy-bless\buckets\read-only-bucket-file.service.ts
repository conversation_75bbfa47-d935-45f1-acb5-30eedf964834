import type { BucketFileSearchDto, FileSearchResultDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyBucketFileService {
  apiName = 'Default';
  

  search = (input: BucketFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<FileSearchResultDto>>({
      method: 'POST',
      url: '/api/app/read-only-bucket-file/search',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
