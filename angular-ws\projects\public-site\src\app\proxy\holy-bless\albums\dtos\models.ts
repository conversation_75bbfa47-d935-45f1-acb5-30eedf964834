import type { AlbumType } from '../../enums/album-type.enum';
import type { AuditedEntityDto, EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentCategory } from '../../enums/content-category.enum';

export interface AlbumAggregateDto {
  id: number;
  channelId?: number;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  languageCode?: string;
  spokenLangCode?: string;
  title?: string;
  description?: string;
  views: number;
  likes: number;
  weight: number;
  albumType?: AlbumType;
  albumFiles: AlbumFileDto[];
}

export interface AlbumDto extends AuditedEntityDto<number> {
  channelId?: number;
  channelName?: string;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  languageCode?: string;
  spokenLangCode?: string;
  title?: string;
  description?: string;
  views: number;
  likes: number;
  weight: number;
  albumType?: AlbumType;
}

export interface AlbumFileDto extends EntityDto {
  fileId: number;
  title?: string;
  weight: number;
  fileName?: string;
  fileUrl?: string;
  mediaType?: MediaType;
  contentCategory?: ContentCategory;
  deliveryDate?: string;
}

export interface AlbumSearchDto extends PagedAndSortedResultRequestDto {
  channelId?: number;
  albumType?: AlbumType;
  channelContentCode?: string;
}
