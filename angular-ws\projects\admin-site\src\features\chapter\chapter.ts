import { Component, computed, inject, signal } from '@angular/core';
import { CTreeTableComponent } from '../../components/c-tree-table/c-tree-table';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { ChapterService } from '@/proxy/holy-bless/books/chapter.service';
import { EBookService } from '@/proxy/holy-bless/books/ebook.service';
import { SelectModule } from 'primeng/select';
import { InputTextModule } from 'primeng/inputtext';
import { cloneDeep } from 'lodash-es';
import { TreeSelectModule } from 'primeng/treeselect';
import { DrawerModule } from 'primeng/drawer';
import { TableModule } from 'primeng/table';
import { BuildFlatData, BuildTreeData } from '../../libs/utils';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-chapter',
  standalone: true,
  templateUrl: './chapter.html',
  styleUrls: ['./chapter.scss'],
  imports: [
    CTreeTableComponent,
    ButtonModule,
    DialogModule,
    FormsModule,
    SelectModule,
    InputTextModule,
    TreeSelectModule,
    DrawerModule,
    TableModule,
  ],
})
export class ChapterComponent {
  #ChapterService = inject(ChapterService);
  #EBookService = inject(EBookService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);

  // select ebook
  ebookOptions = signal<any[]>([]);
  selectedEbookId = signal<number | null>(null);

  // chapter table
  data = signal<any[]>([]);
  cols = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID'), width: '8rem'  },
    { field: 'title', header: this.i18nService.t()('Title') },
  ]);
  chapterTree = signal<any[]>([]);
  chapterMap = new Map();

  // edit dialog
  mode = signal<'edit' | 'create'>('create');
  selectData = signal<any>(null);

  // drawer
  drawerVisible = signal(false);
  subData = signal<any>(null);
  selectSub = signal<any>(null);

  ngOnInit() {
    this.loadEbookData();
  }

  loadData(ebookId: number) {
    this.#LoadingService.loading.set(true);
    this.#ChapterService.getChapterTreeByEBookId(ebookId).subscribe({
      next: (res) => {
        const list: any[] = [];
        BuildFlatData(res || [], list);

        this.data.set(list);
        this.chapterTree.set(
          BuildTreeData(
            list,
            'parentChapterId',
            undefined,
            'title',
            this.chapterMap,
          ),
        );
        this.#LoadingService.loading.set(false);
      },
      error: () => {
        this.#LoadingService.loading.set(false);
      }
    });
  }

  loadEbookData() {
    this.#EBookService.getAllEBooks().subscribe({
      next: (res) => {
        this.ebookOptions.set(res || []);
        if (res?.[0]?.id) {
          this.selectedEbookId.set(res?.[0]?.id);
          this.loadData(res?.[0]?.id);
        }
      },
    });
  }

  handleDrop({ channelId, toParentId, beforeId }: any) {
    this.#ChapterService
      .moveChapter(channelId, toParentId, beforeId)
      .subscribe({
        next: (res) => {
          this.loadData(this.selectedEbookId()!);
        },
      });
  }

  handleCreate() {
    this.mode.set('create');
    this.selectData.set({
      eBookId: this.selectedEbookId(),
    });
  }

  handleEdit(rowData: any) {
    this.mode.set('edit');
    const _rowData = cloneDeep(rowData);
    _rowData.parentChapter = null;
    if (_rowData.parentChapterId) {
      _rowData.parentChapter = {
        key: _rowData.parentChapterId,
        label: this.chapterMap.get(_rowData.parentChapterId)?.title,
      };
    }
    this.selectData.set(_rowData);
  }

  updateSelectChannel() {
    const _data = this.selectData();
    if (_data.parentChapter) {
      _data.parentChapterId = _data.parentChapter.key;
    }
    delete _data.parentChapter;
    if (this.mode() === 'edit') {
      this.#ChapterService.update(_data.id, _data).subscribe({
        next: (res) => {
          this.loadData(this.selectedEbookId()!);
          this.selectData.set(null);
        },
      });
    } else {
      this.#ChapterService.create(_data).subscribe({
        next: (res) => {
          this.loadData(this.selectedEbookId()!);
          this.selectData.set(null);
        },
      });
    }
  }

  handleDelete(rowData: any) {
    this.#ChapterService.delete(rowData.id).subscribe({
      next: (res) => {
        this.loadData(this.selectedEbookId()!);
      },
    });
  }

  handleAdd(rowData: any) {
    this.drawerVisible.set(true);
    this.loadSubData(rowData.id);
  }

  loadSubData(chapterId: number) {
    this.#ChapterService.getChapterArticles(chapterId).subscribe({
      next: (res) => {
        this.subData.set(res || []);
      },
    });
  }

  handleRemove() {
    // this.#ChapterService.removeArticleFromChapter(this.selectSub()).subscribe({
    //   next: (res) => {
    //     this.loadSubData(this.selectData()!.id);
    //   },
    // });
  }
}
