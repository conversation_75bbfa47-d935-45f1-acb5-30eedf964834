import { DragDropModule } from '@angular/cdk/drag-drop';
import { Component, inject, signal, computed } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TableModule } from 'primeng/table';
import { cloneDeep } from 'lodash-es';
import { BucketFileService } from '@/proxy/holy-bless/buckets/bucket-file.service';
import { contentCategoryOptions } from '@/proxy/holy-bless/enums/content-category.enum';
import { DatePickerModule } from 'primeng/datepicker';
import { ConfirmationService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { TableDisplayPipe } from '../../pipes/table-display.pipe';
import { CDragTableComponent } from '../../components/c-drag-table/c-drag-table';
import { EBookService } from '@/proxy/holy-bless/books/ebook.service';
import { TreeSelectModule } from 'primeng/treeselect';
import { ChannelService } from '@/proxy/holy-bless/channels/channel.service';
import { AlbumService } from '@/proxy/holy-bless/albums/album.service';
import { albumTypeOptions } from '@/proxy/holy-bless/enums/album-type.enum';
import { spokenLangList } from '../../libs/constants';
import { DrawerModule } from 'primeng/drawer';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-album',
  templateUrl: './album.html',
  styleUrls: ['./album.scss'],
  standalone: true,
  imports: [
    ButtonModule,
    SelectButtonModule,
    FormsModule,
    InputTextModule,
    DialogModule,
    SelectModule,
    DatePickerModule,
    ConfirmPopupModule,
    TableDisplayPipe,
    TableModule,
    TreeSelectModule,
    DrawerModule,
  ],
  providers: [ConfirmationService],
})
export class AlbumComponent {
  #AlbumService = inject(AlbumService);
  confirmationService = inject(ConfirmationService);
  #ChannelService = inject(ChannelService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);

  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');
  spokenLangList = computed(() =>
    spokenLangList.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );
  albumTypeOptions = computed(() =>
    albumTypeOptions.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  // table
  columns = computed(() => [
    {
      field: 'id',
      header: this.i18nService.t()('ID'),
    },
    {
      field: 'title',
      header: this.i18nService.t()('Title'),
    },
    {
      field: 'albumType',
      header: this.i18nService.t()('Type'),
      type: 'select',
      selectData: this.albumTypeOptions(),
    },
    {
      field: 'channelName',
      header: this.i18nService.t()('Channel'),
    },
    {
      field: 'spokenLangCode',
      header: this.i18nService.t()('SpokenLanguage'),
      type: 'select',
      selectData: this.spokenLangList(),
    },
  ]);
  first = signal(0);
  rows = signal(10);
  totalRecords = signal(0);
  data = signal<any[]>([]);
  selectedRows = signal<any[]>([]);

  // edit dialog
  channelTree = signal<any[]>([]);
  channelMap = new Map();

  // add to channel dialog
  addToChannelVisible = signal(false);
  selectChannel = signal<any>(null);

  // drawer
  drawerVisible = signal(false);
  subData = signal<any[]>([]);
  selectSub = signal<any[]>([]);
  activeAlbum = signal<any>(null);

  ngOnInit() {
    this.loadData();
    this.loadChannelData();
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#AlbumService.getAllAlbums(this.lang()).subscribe({
      next: (res) => {
        this.data.set(res || []);
        this.#LoadingService.loading.set(false);
      },
      error: () => {
        this.#LoadingService.loading.set(false);
      },
    });
  }

  loadChannelData() {
    this.#ChannelService.getAllChannels(this.lang()).subscribe({
      next: (res) => {
        this.channelTree.set(
          this.buildTreeData(
            res,
            undefined,
            undefined,
            undefined,
            this.channelMap,
          ),
        );
      },
    });
  }

  buildTreeData(
    flatData: any[],
    parentKey = 'parentChannelId',
    key = 'id',
    label = 'name',
    map = new Map(),
  ) {
    const result: any[] = [];

    flatData.forEach((item) => {
      map.set(item[key], {
        ...item,
        key: item[key],
        label: item[label],
        children: [],
      });
    });

    flatData.forEach((item) => {
      const node = map.get(item[key]);

      if (!item[parentKey]) {
        result.push(node);
      } else {
        const parent = map.get(item[parentKey]);
        if (parent) {
          parent.children.push(node);
        }
      }
    });
    return result;
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.loadData();
  }

  contentCategoryOptions = contentCategoryOptions;
  selectData = signal<any>(null);

  mode = signal<'create' | 'edit'>('create');

  handleEdit(rowData: any) {
    const _rowData = cloneDeep(rowData);
    if (_rowData.channelId) {
      _rowData.channel = this.channelMap.get(_rowData.channelId);
    }
    if (_rowData.deliveryDate) {
      _rowData.deliveryDate = new Date(_rowData.deliveryDate);
    }
    this.selectData.set(_rowData);
    this.mode.set('edit');
  }

  handleCreate() {
    this.selectData.set({});
    this.mode.set('create');
  }

  updateSelectData() {
    const _selectData = cloneDeep(this.selectData());
    if (_selectData.channel) {
      _selectData.channelId = _selectData.channel.id;
      delete _selectData.channel;
    }
    if (this.mode() === 'edit') {
      this.#AlbumService.update(_selectData.id, _selectData).subscribe({
        next: (res) => {
          this.loadData();
          this.selectData.set(null);
        },
      });
    } else {
      this.#AlbumService.create(_selectData).subscribe({
        next: (res) => {
          this.loadData();
          this.selectData.set(null);
        },
      });
    }
  }

  handleDelete(event: Event, rowData: any) {
    this.confirmationService.confirm({
      target: event.currentTarget as EventTarget,
      message: this.i18nService.t()('DoYouWantToDeleteThisRecord'),
      icon: 'pi pi-info-circle',
      rejectButtonProps: {
        label: this.i18nService.t()('Cancel'),
        severity: 'secondary',
        outlined: true,
      },
      acceptButtonProps: {
        label: this.i18nService.t()('Delete'),
        severity: 'danger',
      },
      accept: () => {
        this.#AlbumService.delete(rowData.id).subscribe({
          next: (res) => {
            this.loadData();
          },
        });
      },
    });
  }

  handleRowReorder(event: any) {
    const { dropIndex } = event;
    const sourceId = this.subData()[dropIndex].fileId;
    const targetId = this.subData()[dropIndex + 1]?.fileId;
    this.#AlbumService.moveAlbumFile(sourceId, targetId).subscribe({
      next: (res: any) => {
        this.handleAdd(this.activeAlbum());
      },
    });
  }

  handleAddToChannel() {
    this.addToChannelVisible.set(true);
  }

  changeChannel() {
    const albumIds = this.selectedRows().map((item) => item.id);
    this.#AlbumService
      .linkToChannelByChannelIdAndAlbumIds(this.selectChannel().id, albumIds)
      .subscribe({
        next: (res) => {
          this.loadData();
          this.selectChannel.set(null);
          this.selectedRows.set([]);
          this.addToChannelVisible.set(false);
        },
      });
  }

  handleAdd(rowData: any) {
    this.activeAlbum.set(rowData);
    this.drawerVisible.set(true);
    this.#AlbumService.getAlbumFiles(rowData.id).subscribe({
      next: (res) => {
        this.subData.set(res.albumFiles || []);
      },
    });
  }

  handleRemove() {
    this.drawerVisible.set(false);
    this.subData.set([]);
  }
}
