import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerModule } from 'primeng/drawer';
import { DividerModule } from 'primeng/divider';
import { ButtonModule } from 'primeng/button';
import { DrawerService } from '../../services/drawer.service';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { I18nService } from '@/services/i18n.service';
import { ThemeService } from '@/services/theme.service';

@Component({
  selector: 'app-setting-drawer',
  standalone: true,
  imports: [
    CommonModule,
    DrawerModule,
    DividerModule,
    ButtonModule,
    MenuModule,
  ],
  templateUrl: './setting-drawer.html',
  styleUrls: ['./setting-drawer.scss'],
})
export class SettingDrawerComponent {
  // 注入 DrawerService
  drawerService = inject(DrawerService);
  i18nService = inject(I18nService);
  themeService = inject(ThemeService);

  audioLanguages = computed((): MenuItem[] =>
    this.i18nService.supportedAudioDevices.map((lang) => ({
      label: lang.label,
      command: () => this.i18nService.setAudioDevice(lang.code),
    })),
  );

  changeFontSize(action: '+' | '-') {
    this.themeService.changeFontSize(action);
  }
}
