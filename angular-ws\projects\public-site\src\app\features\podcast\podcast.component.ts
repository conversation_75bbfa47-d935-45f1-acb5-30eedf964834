import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-podcast',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: ` <router-outlet></router-outlet> `,
  styles: [
    `
      :host {
        flex: 1;
        display: flex;
        justify-content: center;
      }
    `,
  ],
})
export class PodcastComponent {
  playPodcast(podcastId: string) {
    console.log('Playing podcast:', podcastId);
  }

  playEpisode(episodeId: string) {
    console.log('Playing episode:', episodeId);
  }
}
