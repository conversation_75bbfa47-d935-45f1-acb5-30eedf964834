<div class="flex flex-1 flex-col md:flex-row">
  @if((mobileService.isMobile | async)) {
  <app-mobile-catagory-files-drawer
    [leftTemplate]="treeTemplate"
    [rightTemplate]="galleriaTemplate"
    [showRightIcon]="notImageArticleFiles().length > 0"
    [leftDrawerVisible]="leftDrawerVisible"
  />
  } @else {
  <ng-container *ngTemplateOutlet="treeTemplate"></ng-container>
  }
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1"
    [ngClass]="{'overflow-y-auto': !(mobileService.isMobile | async)}"
    [style.max-height]="!(mobileService.isMobile | async) ? 'calc(100vh - 60px)' : 'none'"
  >
    <h1 class="text-3xl font-bold mb-4">{{ articleDetail()?.title }}</h1>
    <p class="text-sm mb-4 flex items-center gap-2">
      @if (articleDetail()) {
      <i class="pi pi-clock"></i>
      {{ articleDetail()?.deliveryDate | date: 'yy-MM-dd' }} }
    </p>
    <div
      class="mb-4 articaldetail-container main-content-text"
      [innerHTML]="articleContent() || '<p class=\'text-sm italic\'>'+i18nService.translate('noContent')+'</p>'"
    ></div>
  </div>
  @if(!(mobileService.isMobile | async)) {
  <div class="p-6 w-[20rem] border-l-2">
    <div>
      <div class="flex flex-col max-h-[60vh] overflow-y-auto">
        <h3 class="text-lg font-semibold mb-4">目录</h3>

        <!-- 如果没有目录项，显示提示 -->
        <div *ngIf="tocItems().length === 0" class="text-sm italic">
          {{i18nService.translate('noCatalog')}}
        </div>

        <!-- 动态生成的目录项 -->
        <a
          *ngFor="let item of tocItems(); trackBy: trackByTocItem"
          class="mt-2 cursor-pointer hover:text-primary-600 underline"
          (click)="onTocItemClick(item)"
        >
          {{ item.text }}
        </a>
      </div>
      <ng-container *ngTemplateOutlet="galleriaTemplate"></ng-container>
    </div>
  </div>
  }
</div>

@if((mobileService.isMobile | async)) {
<app-catalog
  [catalogList]="tocItems()"
  (onItemClick)="onTocItemClick($event)"
></app-catalog>
}

<ng-template #galleriaTemplate>
  <app-play-download
    [notImageArticleFiles]="notImageArticleFiles()"
  ></app-play-download>
</ng-template>

<ng-template #treeTemplate>
  <p-tree
    [value]="files"
    [styleClass]="`${(mobileService.isMobile | async) ? 'w-full' : 'w-[20rem]'} h-full`"
    selectionMode="single"
    [(selection)]="selectedFile"
    (onNodeSelect)="loadArticleDetail($event.node)"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
  />
</ng-template>
