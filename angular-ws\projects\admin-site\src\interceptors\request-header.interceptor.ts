import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { I18nService } from '../services/i18n.service';

export const RequestHeaderInterceptor: HttpInterceptorFn = (req, next) => {
  const i18nService = inject(I18nService);
  const modifiedReq = req.clone({
    setHeaders: {
      'Accept-Language': i18nService.language(),
    },
  });

  return next(modifiedReq);
};
