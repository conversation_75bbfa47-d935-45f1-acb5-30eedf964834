import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-ebooks',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: ` <router-outlet></router-outlet> `,
  styles: [
    `
      :host {
        flex: 1;
        display: flex;
        justify-content: center;
      }
    `,
  ],
})
export class EbooksComponent {
  openBook(bookId: string) {
    console.log('Opening book:', bookId);
    // 这里可以导航到阅读器页面
  }

  navigateToLibrary() {
    console.log('Navigating to library');
    // 导航到书库页面
  }
}
