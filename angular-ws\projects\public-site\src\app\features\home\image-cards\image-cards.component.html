<div class="image-cards-container p-1 md:p-6">
  <!-- 筛选和操作工具栏 -->
  <div
    class="filters-toolbar grid grid-cols-1 md:grid-cols-2 mb-6"
  >
    <p-breadcrumb [model]="breadcrumbItems()" [home]="home"> </p-breadcrumb>
    <div class="flex items-center gap-4 flex md:justify-end">
      <!-- 日期选择器 -->
      <div class="date-picker-container">
        <span class="p-float-label">
          <p-datepicker
            (onSelect)="onDateChange($event)"
            (onClear)="onDateChange(null)"
            showClear
            [(ngModel)]="selectedDate"
            view="month"
            dateFormat="yy-mm"
            [readonlyInput]="true"
            placeholder="{{ i18nService.translate('selectDate') }}"
          />
        </span>
      </div>

      <!-- 播放按钮 -->
      <p-button
        icon="pi pi-play-circle"
        [label]="i18nService.translate('play')"
        [outlined]="true"
        severity="primary"
        (onClick)="playCurrentPage()"
      >
      </p-button>
    </div>
  </div>

  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
    @for (item of cardItems; track item.id) {
      <p-card styleClass="card-item" [id]="'card-' + item.id">
        <ng-template pTemplate="header">
          <img
            [src]="item.thumbnailUrl"
            [alt]="item.title + ' 封面图片'"
            class="card-image rounded-t-[3px]"
          />
        </ng-template>
        <p
          class="cursor-pointer text-[1.1rem] md:text-lg font-semibold"
          (click)="navigateToArticle(item)"
          [innerHTML]="item.title"
        ></p>
        <p
          class="mt-2 text-gray-500 text-sm line-clamp-2 leading-loose"
          [innerHTML]="item.description"
        ></p>
        <ng-template pTemplate="footer">
          <div class="text-gray-500 text-sm flex items-center">
            <i class="pi pi-clock mr-2"></i>
            {{ item.creationTime | date: "yyyy-MM-dd" }}
          </div>
        </ng-template>
      </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [showPageLinks]="!isMobile"
      [showJumpToPageInput]="!isMobile"
      [showJumpToPageDropdown]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div>
</div>
