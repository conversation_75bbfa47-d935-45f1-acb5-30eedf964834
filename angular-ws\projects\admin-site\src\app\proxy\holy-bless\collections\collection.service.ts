import type { CollectionArticleDto, CollectionArticleSearchDto, CollectionArticleTreeDto, CollectionDto, CollectionFileSearchDto, CollectionSearchDto, CollectionToFileDto, CollectionTreeDto, CreateUpdateCollectionDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { ArticleTitleDto } from '../articles/dtos/models';
import type { CollectionSummaryRequest, CollectionSummaryResult } from '../results/models';

@Injectable({
  providedIn: 'root',
})
export class CollectionService {
  apiName = 'Default';
  

  addFilesToCollectionByCollectionIdAndFiles = (collectionId: number, files: CollectionToFileDto[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/collection/files-to-collection/${collectionId}`,
      body: files,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateUpdateCollectionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'POST',
      url: '/api/app/collection',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/collection/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/collection/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllCollections = (languageCode?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto[]>({
      method: 'GET',
      url: '/api/app/collection/collections',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticleTitles = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleTitleDto[]>({
      method: 'GET',
      url: `/api/app/collection/collection-article-titles/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticleTitlesByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleTitleDto[]>({
      method: 'GET',
      url: '/api/app/collection/collection-article-titles-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticles = (input: CollectionArticleSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionArticleDto>>({
      method: 'GET',
      url: '/api/app/collection/collection-articles',
      params: { collectionId: input.collectionId, status: input.status, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getCollectionFiles = (input: CollectionFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionToFileDto>>({
      method: 'GET',
      url: '/api/app/collection/collection-files',
      params: { collectionId: input.collectionId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getCollectionSummary = (collectionId: number, request: CollectionSummaryRequest, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionSummaryResult>({
      method: 'GET',
      url: `/api/app/collection/collection-summary/${collectionId}`,
      params: { skip: request.skip, maxResultCount: request.maxResultCount, sorting: request.sorting, year: request.year, month: request.month },
    },
    { apiName: this.apiName,...config });
  

  getCollectionSummaryByContentCode = (contentCode: string, request: CollectionSummaryRequest, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionSummaryResult>({
      method: 'GET',
      url: '/api/app/collection/collection-summary-by-content-code',
      params: { contentCode, skip: request.skip, maxResultCount: request.maxResultCount, sorting: request.sorting, year: request.year, month: request.month },
    },
    { apiName: this.apiName,...config });
  

  getCollectionTree = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionTreeDto[]>({
      method: 'GET',
      url: `/api/app/collection/collection-tree/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeAndArticleTitles = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionArticleTreeDto[]>({
      method: 'GET',
      url: `/api/app/collection/collection-tree-and-article-titles/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeAndArticleTitlesByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionArticleTreeDto[]>({
      method: 'GET',
      url: '/api/app/collection/collection-tree-and-article-titles-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionTreeDto[]>({
      method: 'GET',
      url: '/api/app/collection/collection-tree-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getFirstByChannelId = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/collection/first-by-channel-id/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getLanguageMatchingCollection = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/collection/language-matching-collection/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getLanguageMatchingCollectionByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: '/api/app/collection/language-matching-collection-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getList = (input: CollectionSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<CollectionDto>>({
      method: 'GET',
      url: '/api/app/collection',
      params: { status: input.status, channelId: input.channelId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndCollectionId = (channelId: number, collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/collection/link-to-channel',
      params: { channelId, collectionId },
    },
    { apiName: this.apiName,...config });
  

  linkToChannelByChannelIdAndCollectionIds = (channelId: number, collectionIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/collection/link-to-channel/${channelId}`,
      body: collectionIds,
    },
    { apiName: this.apiName,...config });
  

  moveCollection = (collectionId: number, toParentId: number, beforeId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/collection/move-collection',
      params: { collectionId, toParentId, beforeId },
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByCollectionId = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/collection/unlink-from-channel/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  unlinkFromChannelByCollectionIds = (collectionIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/collection/unlink-from-channel',
      body: collectionIds,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateCollectionDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'PUT',
      url: `/api/app/collection/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
