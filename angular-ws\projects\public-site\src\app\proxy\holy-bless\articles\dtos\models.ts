import type { AuditedEntityDto, EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { ArticleContentCategory } from '../../enums/article-content-category.enum';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { SearchFieldEnum } from '../../enums/search-field-enum.enum';

export interface ArticleDto extends AuditedEntityDto<number> {
  deliveryDate?: string;
  languageCode?: string;
  title?: string;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  articleContentCategory?: ArticleContentCategory;
  status?: PublishStatus;
  content?: string;
  memo?: string;
}

export interface ArticleSearchDto extends PagedAndSortedResultRequestDto {
  keyword?: string;
  searchFields?: SearchFieldEnum[];
  contentCategories?: ArticleContentCategory[];
  deliveryDateStart?: string;
  deliveryDateEnd?: string;
}

export interface ArticleSearchResultDto {
  id: number;
  title?: string;
  description?: string;
  deliveryDate?: string;
  articleContentCategory?: ArticleContentCategory;
  lastModificationTime?: string;
  content?: string;
}

export interface ArticleTitleDto extends EntityDto<number> {
  title?: string;
  deliveryDate?: string;
}
