{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"public-site": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/public-site", "sourceRoot": "projects/public-site/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/public-site", "index": "projects/public-site/src/index.html", "browser": "projects/public-site/src/main.ts", "polyfills": ["projects/public-site/src/polyfills.ts"], "tsConfig": "projects/public-site/tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["chart.js", "js-sha256"], "assets": ["projects/public-site/src/assets", {"glob": "**/*", "input": "projects/public-site/public", "output": ""}, {"glob": "preloading.css", "input": "projects/public-site/src/assets/styles", "output": "/assets/styles"}, "projects/public-site/src/manifest.webmanifest"], "stylePreprocessorOptions": {"includePaths": ["projects/shared-theme"]}, "styles": ["node_modules/video.js/dist/video-js.css", "projects/public-site/src/styles.css", "projects/public-site/src/assets/styles/preloading.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "3.5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "100kB"}], "fileReplacements": [{"replace": "projects/public-site/src/environments/environment.ts", "with": "projects/public-site/src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "extractLicenses": true, "namedChunks": false, "serviceWorker": "projects/public-site/ngsw-config.json"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "public-site:build:production"}, "development": {"buildTarget": "public-site:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "projects/public-site/src/proxy.conf.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/public-site/src/test.ts", "polyfills": ["projects/public-site/src/polyfills.ts", "zone.js/testing"], "tsConfig": "projects/public-site/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/public-site/src/assets", {"glob": "**/*", "input": "projects/public-site/public", "output": ""}], "styles": ["projects/public-site/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/public-site/src/**/*.ts", "projects/public-site/src/**/*.html"]}}}}, "admin-site": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/admin-site", "sourceRoot": "projects/admin-site/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/admin-site", "index": "projects/admin-site/src/index.html", "browser": "projects/admin-site/src/main.ts", "polyfills": ["projects/admin-site/src/polyfills.ts"], "tsConfig": "projects/admin-site/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/admin-site/src/assets", {"glob": "**/*", "input": "projects/admin-site/public", "output": ""}], "stylePreprocessorOptions": {"includePaths": ["projects/shared-theme"]}, "styles": ["projects/admin-site/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "2.5MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "100kB"}], "fileReplacements": [{"replace": "projects/admin-site/src/environments/environment.ts", "with": "projects/admin-site/src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "extractLicenses": true, "namedChunks": false}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "admin-site:build:production"}, "development": {"buildTarget": "admin-site:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/admin-site/src/test.ts", "polyfills": ["projects/admin-site/src/app/polyfills.ts", "zone.js/testing"], "tsConfig": "projects/admin-site/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/admin-site/src/assets", {"glob": "**/*", "input": "projects/admin-site/public", "output": ""}], "styles": ["projects/admin-site/src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/admin-site/src/**/*.ts", "projects/admin-site/src/**/*.html"]}}}}}}