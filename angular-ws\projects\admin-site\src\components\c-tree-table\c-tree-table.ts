import {
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  Input,
  Output,
  signal,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ConfirmationService, TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { TreeTableModule } from 'primeng/treetable';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { TableDisplayPipe } from '../../pipes/table-display.pipe';
import { I18nService } from '../../services/i18n.service';
import { CommonModule } from '@angular/common';

export interface ColType {
  field: string;
  header: string;
}

@Component({
  selector: 'c-tree-table',
  templateUrl: './c-tree-table.html',
  styleUrls: ['./c-tree-table.scss'],
  imports: [
    TreeTableModule,
    DialogModule,
    ButtonModule,
    FormsModule,
    InputTextModule,
    ConfirmPopupModule,
    TableDisplayPipe,
    CommonModule,
  ],
  providers: [ConfirmationService],
})
export class CTreeTableComponent {
  @Input('data') _data = signal<any[]>([]);
  @Input() cols: ColType[] = [];
  @Input() parentKey = 'parentChannelId';
  @Input() expandAll = signal(false);
  @ViewChild('tt') tt: any;
  @Output() onDrop = new EventEmitter<any>();
  @Output() onEdit = new EventEmitter<any>();
  @Output() onAdd = new EventEmitter<any>();
  @Output() onDelete = new EventEmitter<any>();

  confirmationService = inject(ConfirmationService);
  i18nService = inject(I18nService);

  expandedSet = new Set<number>();

  // 拖拽相关属性
  dragRow = signal<any>(null);
  dropRow = signal<any>(null);
  isDragging = signal(false);
  dropPosition = signal<'top' | 'middle' | 'bottom' | null>(null);

  map = new Map();

  selectChannel = signal<any>(null);

  mode = signal<'create' | 'edit'>('create');
  data = computed(() => {
    const map = this.map;
    const result: any[] = [];

    this._data().forEach((channel) => {
      map.set(channel.id!, {
        data: channel,
        children: [],
        expanded: this.expandedSet.has(channel.id!),
      });
    });

    this._data().forEach((channel) => {
      const node = map.get(channel.id!);

      if (!channel[this.parentKey]) {
        result.push(node);
      } else {
        const parent = map.get(channel[this.parentKey]);
        if (parent) {
          parent.children.push(node);
        }
      }
    });
    return result;
  });

  // 开始拖拽
  handleDragStart(event: DragEvent, rowData: any) {
    this.dragRow.set(rowData);
    this.isDragging.set(true);
  }

  // 拖拽经过
  handleDragOver(event: DragEvent, rowData: any) {
    event.preventDefault();

    const target = event.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const height = rect.height;

    // 计算鼠标相对位置
    const relativePosition = y / height;

    if (relativePosition < 0.33) {
      this.dropPosition.set('top');
    } else if (relativePosition > 0.66) {
      this.dropPosition.set('bottom');
    } else {
      this.dropPosition.set('middle');
    }

    this.dropRow.set(rowData);
  }

  // 拖拽进入
  handleDragEnter(event: DragEvent, rowData: any) {
    event.preventDefault();
    this.dropRow.set(rowData.id);
  }

  // 拖拽离开
  handleDragLeave(event: DragEvent) {}

  // 放置
  handleDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(false);
    const channelId = this.dragRow().id;
    let toParentId = null;
    let beforeId = null;
    if (this.dropPosition() === 'top') {
      toParentId = this.dropRow()[this.parentKey];
      beforeId = this.dropRow().id;
    } else if (this.dropPosition() === 'bottom') {
      toParentId = this.dropRow()[this.parentKey];
      const list = this.map.get(toParentId).children;
      const findIndex = list.findIndex(
        (child: any) => child.data.id === this.dropRow().id,
      );
      beforeId = list[findIndex + 1]?.data.id || null;
    } else {
      toParentId = this.dropRow().id;
    }
    if (channelId === toParentId) return;
    this.onDrop.emit({
      channelId,
      toParentId,
      beforeId,
    });
  }

  expand(node: any) {
    this.expandedSet.add(node.data.id);
  }

  collapse(node: any) {
    this.expandedSet.delete(node.data.id);
  }

  onFilter(event: Event, field: string, filterMatchMode: string) {
    const target = event.target as HTMLInputElement;
    if (target) {
      this.tt.filter(target.value, field, filterMatchMode);
    }
  }

  handleDelete(event: Event, rowData: any) {
    this.confirmationService.confirm({
      target: event.currentTarget as EventTarget,
      message: this.i18nService.t()('DoYouWantToDeleteThisRecord'),
      icon: 'pi pi-info-circle',
      rejectButtonProps: {
        label: this.i18nService.t()('Cancel'),
        severity: 'secondary',
        outlined: true,
      },
      acceptButtonProps: {
        label: this.i18nService.t()('Delete'),
        severity: 'danger',
      },
      accept: () => {
        this.onDelete.emit(rowData);
      },
    });
  }
}
