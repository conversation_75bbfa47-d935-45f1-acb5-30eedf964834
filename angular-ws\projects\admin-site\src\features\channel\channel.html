<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <p-button
      [label]="i18nService.t()('Create')"
      [text]="true"
      (onClick)="handleCreate()"
    ></p-button>
  </div>
  <c-tree-table
    [data]="data"
    [cols]="cols()"
    (onDrop)="handleDrop($event)"
    (onEdit)="handleEdit($event)"
    (onDelete)="handleDelete($event)"
    (onAdd)="handleAdd($event)"
  ></c-tree-table>
</div>

<p-drawer
  [(visible)]="drawerVisible"
  position="right"
  [style]="{ width: '40rem' }"
>
  <p-table
    [value]="subData()"
    [reorderableColumns]="true"
    [(selection)]="selectSub"
    stripedRows
    (onRowReorder)="handleRowReorder($event)"
  >
    <ng-template #header>
      <tr>
        <th style="width: 4rem"></th>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th style="width: 4rem">{{i18nService.t()('ID')}}</th>
        <th>{{i18nService.t()('Title')}}</th>
        <th>
          <p-button
            [text]="true"
            icon="pi pi-trash"
            [label]="i18nService.t()('Remove')"
            severity="danger"
            (onClick)="handleRemove()"
          ></p-button>
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr [pReorderableRow]="index">
        <td>
          <span class="pi pi-bars" pReorderableRowHandle></span>
        </td>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        <td>{{rowData.id}}</td>
        <td>{{rowData.name}}</td>
        <td></td>
      </tr>
    </ng-template>
  </p-table>
</p-drawer>

<p-dialog
  [header]="mode() === 'edit' ? i18nService.t()('Edit') : i18nService.t()('Create')"
  [modal]="true"
  [(visible)]="selectChannel"
  [style]="{ width: '25rem' }"
>
  @if(selectChannel()) {
  <div class="form">
    <div class="form-item">
      <label>{{i18nService.t()('Title')}}</label>
      <input type="text" pInputText [(ngModel)]="selectChannel().name" />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('ContentCode')}}</label>
      <input type="text" pInputText [(ngModel)]="selectChannel().contentCode" />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('ChannelSource')}}</label>
      <p-select
        [options]="channelSourceOptions()"
        [(ngModel)]="selectChannel().channelSource"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
  </div>
  }
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="updateSelectChannel()"
      [label]="i18nService.t()('Confirm')"
    ></p-button>
  </div>
</p-dialog>
