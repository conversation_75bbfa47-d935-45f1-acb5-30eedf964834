import type { ArticleDto, ArticleSearchDto, ArticleSearchResultDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { ArticleAggregateResult } from '../results/models';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyArticleService {
  apiName = 'Default';
  

  getArticleAggregate = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult>({
      method: 'GET',
      url: `/api/app/read-only-article/article-aggregate/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregateByDeliveryDate = (deliveryDate: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult>({
      method: 'GET',
      url: '/api/app/read-only-article/article-aggregate-by-delivery-date',
      params: { deliveryDate },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByChapterId = (chapterId: number, skipCount?: number, maxResultCount: number = 20, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: `/api/app/read-only-article/article-aggregates-by-chapter-id/${chapterId}`,
      params: { skipCount, maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByCollectionContentCode = (collectionContentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: '/api/app/read-only-article/article-aggregates-by-collection-content-code',
      params: { collectionContentCode },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByCollectionId = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: `/api/app/read-only-article/article-aggregates-by-collection-id/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedIdByArticleId = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, number>({
      method: 'GET',
      url: `/api/app/read-only-article/matched-id/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getRelatedArticlesByFileIdByFileId = (fileId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto[]>({
      method: 'GET',
      url: `/api/app/read-only-article/related-articles-by-file-id/${fileId}`,
    },
    { apiName: this.apiName,...config });
  

  search = (input: ArticleSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleSearchResultDto>>({
      method: 'POST',
      url: '/api/app/read-only-article/search',
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
