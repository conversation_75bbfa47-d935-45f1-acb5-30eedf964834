import type { ArticleFileDto, ArticleFileSearchDto, CreateUpdateArticleFileDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ArticleFileService {
  apiName = 'Default';
  

  create = (input: CreateUpdateArticleFileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto>({
      method: 'POST',
      url: '/api/app/article-file',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/article-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto>({
      method: 'GET',
      url: `/api/app/article-file/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getByArticleId = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto[]>({
      method: 'GET',
      url: `/api/app/article-file/by-article-id/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: ArticleFileSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleFileDto>>({
      method: 'GET',
      url: '/api/app/article-file',
      params: { articleId: input.articleId, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getPrimaryFiles = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto[]>({
      method: 'GET',
      url: `/api/app/article-file/primary-files/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  setPrimary = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto>({
      method: 'POST',
      url: `/api/app/article-file/${id}/set-primary`,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateArticleFileDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleFileDto>({
      method: 'PUT',
      url: `/api/app/article-file/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
