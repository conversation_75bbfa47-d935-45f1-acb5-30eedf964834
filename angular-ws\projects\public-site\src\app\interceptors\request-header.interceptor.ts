import { I18nService } from '@/services/i18n.service';
import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';

export const RequestHeaderInterceptor: HttpInterceptorFn = (req, next) => {
  const i18nService = inject(I18nService);
  const modifiedReq = req.clone({
    setHeaders: {
      'Accept-Language': i18nService.language(),
      //'X-User-Language-Reading': i18nService.language(),
      'X-User-Language-Audio': i18nService.audioDevice(),
      'X-User-Language-UUID': i18nService.userUUID(),
      'X-User-Language-Country': i18nService.countryCode(),
    },
  });

  return next(modifiedReq);
};
