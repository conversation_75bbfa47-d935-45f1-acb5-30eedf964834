import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { TreeType } from '../enums/tree-type.enum';

@Injectable({
  providedIn: 'root',
})
export class TreeJsonSnapshotService {
  apiName = 'Default';
  

  clearCache = (treeType?: enum, rootId?: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/tree-json-snapshot/clear-cache',
      params: { treeType, rootId },
    },
    { apiName: this.apiName,...config });
  

  getTreeJson = (treeType: TreeType, rootId?: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, string>({
      method: 'GET',
      responseType: 'text',
      url: '/api/app/tree-json-snapshot/tree-json',
      params: { treeType, rootId },
    },
    { apiName: this.apiName,...config });
  

  refreshTreeJson = (treeType: TreeType, rootId?: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: '/api/app/tree-json-snapshot/refresh-tree-json',
      params: { treeType, rootId },
    },
    { apiName: this.apiName,...config });
  

  updateTreeJson = (treeType: TreeType, rootId: number, treeJsonData: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'PUT',
      url: `/api/app/tree-json-snapshot/tree-json/${rootId}`,
      params: { treeType, treeJsonData },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
