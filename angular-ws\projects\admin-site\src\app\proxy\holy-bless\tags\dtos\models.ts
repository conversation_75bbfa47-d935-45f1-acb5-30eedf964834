import type { EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';

export interface CreateUpdateTagDto {
  contentCode: string;
  tagName: string;
  languageCode: string;
}

export interface TagDto extends EntityDto<number> {
  contentCode?: string;
  tagName?: string;
  languageCode?: string;
  views: number;
}

export interface TagSearchDto extends PagedAndSortedResultRequestDto {
  tagName?: string;
  contentCode?: string;
}
