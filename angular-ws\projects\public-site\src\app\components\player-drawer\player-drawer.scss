::ng-deep .player-drawer.p-drawer {
  background: #565656;
  @apply w-[25rem];
  .p-button-secondary {
    &:hover {
      background: #27272a;
    }
  }
}

img {
  @apply cursor-pointer hover:scale-110 transition-transform duration-200;
}

.volume-control {
  position: relative;
}

:global(.volume-dropdown) {
  .p-overlaypanel-content {
    padding: 0;
  }
}

.volume-slider {
  min-width: 80px;

  .volume-slider-container {
    position: relative;

    .volume-fill {
      background: linear-gradient(to top, #3b82f6, #60a5fa);
    }

    .volume-handle {
      transform: translateX(-50%);

      &:hover {
        transform: translateX(-50%) scale(1.1);
      }

      &.dragging {
        transform: translateX(-50%) scale(1.2);
      }
    }
  }
}

.volume-presets {
  button {
    transition: all 0.2s ease;

    &:hover {
      background-color: #e5e7eb;
      transform: translateY(-1px);
    }
  }
}

// 音量图标动画
.volume-icon {
  i {
    transition: all 0.2s ease;

    &.pi-volume-off {
      color: #ef4444;
    }

    &.pi-volume-down {
      color: #f59e0b;
    }

    &.pi-volume-up {
      color: #10b981;
    }
  }
}

.custom-video-controls {
  // Webkit/Chrome/Safari
  &::-webkit-media-controls-play-button {
    display: none !important;
  }

  &::-webkit-media-controls-mute-button {
    display: none !important;
  }

  &::-webkit-media-controls-volume-slider {
    display: none !important;
  }

  // Firefox
  &::-moz-media-controls-play-button {
    display: none !important;
  }

  &::-moz-media-controls-mute-button {
    display: none !important;
  }

  &::-moz-media-controls-volume-slider {
    display: none !important;
  }
}
.active {
  @apply text-[#fff];
}
::ng-deep .p-slider-handle {
  height: 10px;
  width: 10px;
}
::ng-deep .p-slider-handle::before {
  content: "";
  width: 10px;
  height: 10px;
  display: block;
  background: var(--p-slider-handle-content-background);
  border-radius: var(--p-slider-handle-content-border-radius);
  box-shadow: var(--p-slider-handle-content-shadow);
  transition: background var(--p-slider-transition-duration);
}
