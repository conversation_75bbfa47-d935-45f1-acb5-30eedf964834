<p-drawer
  class="w-50"
  [visible]="drawerService.settingVisible()"
  [header]="i18nService.translate('settings')"
  [position]="'right'"
  [modal]="true"
  (onHide)="drawerService.closeSettings()"
>
  <!-- 设置内容 -->
  <div class="drawer-content">
    <div class="setting-item flex justify-between items-center">
      <label>{{ i18nService.translate('audio_lan_settings') }}</label>
      <div class="language-selector flex items-center">
        <p-menu
          #menu1
          [model]="audioLanguages()"
          [popup]="true"
          appendTo="body"
        />
        <p-button
          (click)="menu1.toggle($event)"
          [label]="i18nService.currentAudioDeviceInfo().label"
          [text]="true"
          size="small"
        />
      </div>
    </div>
    <div class="setting-item flex justify-between items-center">
      <label>{{ i18nService.translate('theme') }}</label>
      <div class="language-selector flex items-center gap-2">
        @for (item of themeService.themes; track $index) {
          <div
            class="w-6 h-6 rounded-full cursor-pointer"
            [style]="{ 'background-color': item['color'] }"
            (click)="themeService.setTheme(item.mode)"
          ></div>
        }
      </div>
    </div>
    <div class="setting-item flex justify-between items-center">
      <label>{{ i18nService.translate('font_size') }}</label>
      <div class="language-selector flex items-center gap-2">
        <p-button
          label="A-"
          outlined
          size="small"
          (click)="changeFontSize('-')"
        ></p-button>
        <p-button
          label="A+"
          outlined
          size="small"
          (click)="changeFontSize('+')"
        ></p-button>
      </div>
    </div>
  </div>
</p-drawer>
