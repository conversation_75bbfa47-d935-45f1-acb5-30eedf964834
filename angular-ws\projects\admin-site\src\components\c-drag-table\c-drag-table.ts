import { Component, Input, signal } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';

@Component({
  selector: 'c-drag-table',
  templateUrl: './c-drag-table.html',
  styleUrls: ['./c-drag-table.scss'],
  standalone: true,
  imports: [TableModule, ButtonModule],
})
export class CDragTableComponent {
  @Input() data: any[] = [];
  @Input() columns: any[] = [];

  dragRow = signal<any>(null);
  dropRow = signal<any>(null);
  isDragging = signal(false);
  dropPosition = signal<'top' | 'middle' | 'bottom' | null>(null);

  // 开始拖拽
  handleDragStart(event: DragEvent, rowData: any) {
    this.dragRow.set(rowData);
    this.isDragging.set(true);
    console.log('开始拖拽', rowData.id);
  }

  // 拖拽经过
  handleDragOver(event: DragEvent, rowData: any) {
    event.preventDefault();

    const target = event.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const height = rect.height;

    // 计算鼠标相对位置
    const relativePosition = y / height;

    if (relativePosition < 0.33) {
      this.dropPosition.set('top');
    } else if (relativePosition > 0.66) {
      this.dropPosition.set('bottom');
    } else {
      this.dropPosition.set('middle');
    }

    this.dropRow.set(rowData);
    console.log('拖拽经过', rowData.id, this.dropPosition());
  }

  // 拖拽进入
  handleDragEnter(event: DragEvent, rowData: any) {
    event.preventDefault();
    this.dropRow.set(rowData.id);
    console.log('拖拽进入', rowData.id);
  }

  // 拖拽离开
  handleDragLeave(event: DragEvent) {}

  // 放置
  handleDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(false);
  }
}
