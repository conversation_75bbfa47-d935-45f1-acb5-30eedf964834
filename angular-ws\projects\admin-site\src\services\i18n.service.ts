import { computed, effect, Injectable, signal } from '@angular/core';
import { get } from 'lodash-es';

export enum EyeTypes {
  'zh-Hans' = 'zh-Hans',
  'zh-Hant' = 'zh-Hant',
  'en' = 'en',
}

export interface Language {
  code: keyof typeof EyeTypes;
  label: string;
}

@Injectable({
  providedIn: 'root',
})
export class I18nService {
  supportedLanguages = signal([
    { code: 'zh-Hans', label: '简体中文' },
    { code: 'zh-Hant', label: '繁體中文' },
    { code: 'en', label: 'English' },
  ]);
  language = signal<string>(this.getStoredLanguage() || 'zh-Hans');
  currentLanguageInfo = computed(
    () =>
      this.supportedLanguages().find((lang) => lang.code === this.language()) ||
      this.supportedLanguages()[0],
  );
  translations = signal<Record<string, any>>({});
  t = computed(() => {
    const lang = this.language();
    const resources = this.translations();
    return (key: string): string => get(resources[lang], key) || key;
  });

  getStoredLanguage() {
    return localStorage.getItem('lang') || EyeTypes['zh-Hans'];
  }

  constructor() {
    this.loadTranslations();
    effect(() => {
      localStorage.setItem('lang', this.language());
    });
  }

  private async loadTranslations() {
    try {
      const [zhCN, zhTW, enUS] = await Promise.all([
        import('./../assets/i18n/zh-hans.json'),
        import('./../assets/i18n/zh-hant.json'),
        import('./../assets/i18n/en.json'),
      ]);

      this.translations.set({
        'zh-Hans': zhCN.default,
        'zh-Hant': zhTW.default,
        en: enUS.default,
      });
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  }
}
