import type { ArticleAdminSearchDto, ArticleDto, ArticleSearchDto, ArticleSearchResultDto, ArticleToTagDto, CreateUpdateArticleDto, TeacherArticleLinkDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { ArticleAggregateResult } from '../results/models';
import type { TagDto } from '../tags/dtos/models';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  apiName = 'Default';
  

  addTags = (articleId: number, tagIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleToTagDto[]>({
      method: 'POST',
      url: `/api/app/article/tags/${articleId}`,
      body: tagIds,
    },
    { apiName: this.apiName,...config });
  

  addTeacherArticleLinksByStudentArticleIdAndTeacherLinks = (studentArticleId: number, teacherLinks: TeacherArticleLinkDto[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'POST',
      url: `/api/app/article/teacher-article-links/${studentArticleId}`,
      body: teacherLinks,
    },
    { apiName: this.apiName,...config });
  

  create = (input: CreateUpdateArticleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto>({
      method: 'POST',
      url: '/api/app/article',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/article/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto>({
      method: 'GET',
      url: `/api/app/article/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregate = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult>({
      method: 'GET',
      url: `/api/app/article/article-aggregate/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregateByDeliveryDate = (deliveryDate: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult>({
      method: 'GET',
      url: '/api/app/article/article-aggregate-by-delivery-date',
      params: { deliveryDate },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByChapterId = (chapterId: number, skipCount?: number, maxResultCount: number = 20, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: `/api/app/article/article-aggregates-by-chapter-id/${chapterId}`,
      params: { skipCount, maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByCollectionContentCode = (collectionContentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: '/api/app/article/article-aggregates-by-collection-content-code',
      params: { collectionContentCode },
    },
    { apiName: this.apiName,...config });
  

  getArticleAggregatesByCollectionId = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleAggregateResult[]>({
      method: 'GET',
      url: `/api/app/article/article-aggregates-by-collection-id/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: ArticleAdminSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleDto>>({
      method: 'GET',
      url: '/api/app/article',
      params: { languageCode: input.languageCode, sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getMatchedIdByArticleId = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, number>({
      method: 'GET',
      url: `/api/app/article/matched-id/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getRelatedArticlesByFileIdByFileId = (fileId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto[]>({
      method: 'GET',
      url: `/api/app/article/related-articles-by-file-id/${fileId}`,
    },
    { apiName: this.apiName,...config });
  

  getTags = (articleId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, TagDto[]>({
      method: 'GET',
      url: `/api/app/article/tags/${articleId}`,
    },
    { apiName: this.apiName,...config });
  

  getTeacherArticleLinks = (studentArticleId: number, skipCount: number, maxResultCount: number, sorting?: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<TeacherArticleLinkDto>>({
      method: 'GET',
      url: `/api/app/article/teacher-article-links/${studentArticleId}`,
      params: { skipCount, maxResultCount, sorting },
    },
    { apiName: this.apiName,...config });
  

  removeTagsFromArticleByArticleIdAndTagIds = (articleId: number, tagIds: number[], config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/article/tags-from-article/${articleId}`,
      params: { tagIds },
    },
    { apiName: this.apiName,...config });
  

  search = (input: ArticleSearchDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<ArticleSearchResultDto>>({
      method: 'POST',
      url: '/api/app/article/search',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateArticleDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleDto>({
      method: 'PUT',
      url: `/api/app/article/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
