<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <div class="flex gap-2">
      <p-button
        [text]="true"
        [label]="i18nService.t()('Create')"
        (onClick)="handleCreate()"
      ></p-button>
      <p-button
        [text]="true"
        severity="danger"
        [label]="i18nService.t()('Delete')"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToCollection')"
        (onClick)="handleAddToCollection()"
        [disabled]="!selectedRows().length"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToChapter')"
        (onClick)="handleAddToChapter()"
        [disabled]="!selectedRows().length"
      ></p-button>
    </div>
  </div>
  <p-table
    [value]="data()"
    [paginator]="true"
    [first]="first()"
    [rows]="rows()"
    [columns]="columns()"
    [totalRecords]="totalRecords()"
    [lazy]="true"
    (onPage)="onPageChange($event)"
    [showCurrentPageReport]="true"
    stripedRows
    [(selection)]="selectedRows"
    [scrollable]="true"
  >
    <ng-template #header let-columns>
      <tr>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        @for(col of columns; track col.field) {
        <th pReorderableColumn>
          <div class="flex items-center">
            {{col.header}}
            <p-columnFilter type="text" [field]="col.field" display="menu" />
          </div>
        </th>
        }
        <th></th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        @for(col of columns; track col.field) {
        <td>
          <div class="truncate max-w-[20rem]">
            {{rowData[col.field] | tableDisplay: {type: col.type, selectData:
            col.selectData} }}
          </div>
        </td>
        }
        <td>
          <p-button
            icon="pi pi-pencil"
            [text]="true"
            styleClass="!rounded-full"
            (onClick)="handleEdit(rowData)"
          ></p-button>
          <p-button
            icon="pi pi-folder"
            [text]="true"
            styleClass="!rounded-full"
            (onClick)="handleAdd(rowData)"
          ></p-button>
          <p-button
            icon="pi pi-trash"
            [text]="true"
            severity="danger"
            styleClass="!rounded-full"
            (onClick)="handleDelete($event, rowData)"
          ></p-button>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-confirmpopup />

<p-drawer
  [(visible)]="drawerVisible"
  position="right"
  [style]="{ width: '40rem' }"
>
  <p-table
    [value]="subData()"
    [reorderableColumns]="true"
    [(selection)]="selectSub"
    stripedRows
  >
    <ng-template #header>
      <tr>
        <th style="width: 4rem"></th>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th style="width: 4rem">{{i18nService.t()('ID')}}</th>
        <th>{{i18nService.t()('Title')}}</th>
        <th>
          <p-button
            [text]="true"
            icon="pi pi-trash"
            [label]="i18nService.t()('Remove')"
            severity="danger"
            (onClick)="handleRemove()"
          ></p-button>
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr [pReorderableRow]="index">
        <td>
          <span class="pi pi-bars" pReorderableRowHandle></span>
        </td>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        <td>{{rowData.id}}</td>
        <td>{{rowData.title}}</td>
        <td></td>
      </tr>
    </ng-template>
  </p-table>
</p-drawer>

<p-dialog
  [header]="i18nService.t()('AddToCollection')"
  [modal]="true"
  [(visible)]="addToCollectionVisible"
  [style]="{ width: '25rem' }"
>
  <div class="form">
    <div class="form-item">
      <label>{{i18nService.t()('Collection')}}</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectCollection"
        [options]="collectionTree()"
        appendTo="body"
      />
    </div>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="handleChangeCollection()"
      [label]="i18nService.t()('Confirm')"
    ></p-button>
  </div>
</p-dialog>

<p-dialog
  [header]="'Add to chapter'"
  [modal]="true"
  [(visible)]="addToChapterVisible"
  [style]="{ width: '25rem' }"
>
  <div class="form">
    <div class="form-item">
      <label>{{i18nService.t()('Ebook')}}</label>
      <p-select
        containerStyleClass="w-full"
        [(ngModel)]="selectEbook"
        [options]="ebookList()"
        optionLabel="title"
        optionValue="id"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Chapter')}}</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectChapter"
        [options]="chapterTree()"
        appendTo="body"
      />
    </div>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="handleChangeChapter()"
      [label]="i18nService.t()('Confirm')"
    ></p-button>
  </div>
</p-dialog>
