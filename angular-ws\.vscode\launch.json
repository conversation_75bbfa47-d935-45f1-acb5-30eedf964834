{
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "debug public-site",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "debug-public-site",
      "url": "http://localhost:4200/"
    },
    {
      "name": "debug-admin-site",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "debug-admin-site",
      "url": "http://localhost:4201/"
    },
    {
      "name": "ng test",
      "type": "chrome",
      "request": "launch",
      "preLaunchTask": "npm: test",
      "url": "http://localhost:9876/debug.html"
    },
    {
      "type": "pwa-chrome",
      "request": "launch",
      "name": "Attach to Chrome",
      "url": "http://localhost:4200", // 确保和 ng serve 的端口一致
      "webRoot": "${workspaceFolder}"
    }
  ]
}
