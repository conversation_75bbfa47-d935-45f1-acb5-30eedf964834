import { DragDropModule } from '@angular/cdk/drag-drop';
import { Component, signal, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TableModule } from 'primeng/table';
import { TreeTableModule } from 'primeng/treetable';
import { ChannelTypes } from '../../libs/constants';
import { JsonPipe } from '@angular/common';

@Component({
  selector: 'app-channel',
  templateUrl: './channel.html',
  styleUrls: ['./channel.scss'],
  standalone: true,
  imports: [
    TableModule,
    ButtonModule,
    TreeTableModule,
    DragDropModule,
    SelectButtonModule,
    FormsModule,
    InputTextModule,
    DialogModule,
    SelectModule,
    JsonPipe,
  ],
})
export class ChannelComponent {
  value = 'zh-Hans';

  justifyOptions: any[] = [
    { label: 'zh-Hans' },
    { label: 'zh-Hant' },
    { label: 'en' },
  ];

  visible = signal(false);

  cols = [
    { field: 'id', header: 'ID' },
    { field: 'title', header: 'Title' },
    { field: 'contentCode', header: 'Content Code' },
    { field: 'channelSource', header: 'Channel Source' },
    { field: 'weight', header: 'Weight' },
  ];

  files = signal([
    {
      data: {
        id: '1000',
        title: 'Documents',
        contentCode: '',
        channelSource: '',
        weight: '',
      },
      children: [
        {
          data: {
            id: '1001',
            title: 'Work',
            contentCode: 'Work Folder',
            channelSource: '',
            weight: '',
          },
          children: [
            {
              data: {
                id: '10001',
                title: 'Expenses.doc',
                contentCode: 'Document',
                channelSource: 'Channel 1',
                weight: '1',
              },
            },
            {
              data: {
                id: '10002',
                title: 'Resume.doc',
                contentCode: 'Document',
                channelSource: 'Channel 2',
                weight: '2',
              },
            },
          ],
        },
        {
          data: {
            id: '1002',
            title: 'Home',
            contentCode: 'Home Folder',
            channelSource: '',
            weight: '',
          },
          children: [
            {
              data: {
                id: '10003',
                title: 'Invoices.txt',
                contentCode: 'Text',
                channelSource: 4,
                weight: '3',
              },
            },
          ],
        },
      ],
    },
  ]);

  fileMap = new Map();

  ngOnInit() {
    this.flatFiles(this.files());
  }

  flatFiles(nodes: TreeNode[]) {
    nodes.forEach((node) => {
      this.fileMap.set(node.data.id, node);
      if (node.children) {
        this.flatFiles(node.children);
      }
    });
  }

  // 拖拽相关属性
  dragRow = signal<any>(null);
  dropRow = signal<any>(null);
  isDragging = signal(false);
  dropPosition = signal<'top' | 'middle' | 'bottom' | null>(null);

  // 开始拖拽
  onDragStart(event: DragEvent, rowData: any) {
    this.dragRow.set(rowData);
    this.isDragging.set(true);
  }

  // 拖拽经过
  onDragOver(event: DragEvent, rowData: any) {
    event.preventDefault();

    const target = event.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    const y = event.clientY - rect.top;
    const height = rect.height;

    // 计算鼠标相对位置
    const relativePosition = y / height;

    if (relativePosition < 0.33) {
      this.dropPosition.set('top');
    } else if (relativePosition > 0.66) {
      this.dropPosition.set('bottom');
    } else {
      this.dropPosition.set('middle');
    }

    this.dropRow.set(rowData);
  }

  // 拖拽进入
  onDragEnter(event: DragEvent, rowData: any) {
    event.preventDefault();
    this.dropRow.set(rowData.id);
  }

  // 拖拽离开
  onDragLeave(event: DragEvent) {}

  // 放置
  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragging.set(false);
  }

  @ViewChild('tt') tt: any;
  onFilter(event: Event, field: string, filterMatchMode: string) {
    const target = event.target as HTMLInputElement;
    if (target) {
      this.tt.filter(target.value, field, filterMatchMode);
    }
  }

  ChannelTypes = ChannelTypes;
  selectChannel = signal<any>(null);
}
