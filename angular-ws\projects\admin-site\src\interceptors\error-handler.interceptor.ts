import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { MessageService } from 'primeng/api';

export const ErrorHandlerInterceptor: HttpInterceptorFn = (req, next) => {
  const messageService = inject(MessageService);

  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status !== 200 && error.status !== 0) {
        let errorMessage = '';

        switch (error.status) {
          case 400:
            errorMessage = 'Bad Request';
            break;
          case 401:
            errorMessage = 'Unauthorized';
            break;
          case 403:
            errorMessage = 'Forbidden';
            break;
          case 404:
            errorMessage = 'Not Found';
            break;
          case 500:
            errorMessage = 'Internal Server Error';
            break;
          default:
            errorMessage = `HTTP Error: ${error.status}`;
        }

        // 显示错误消息
        messageService.add({
          severity: 'error',
          summary: 'Request Failed',
          detail: errorMessage,
        });

        console.error('HTTP Error:', error);
      }

      return throwError(() => error);
    }),
  );
};
