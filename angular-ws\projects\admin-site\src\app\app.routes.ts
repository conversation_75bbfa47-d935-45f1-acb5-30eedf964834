import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/channel',
    pathMatch: 'full',
  },
  {
    path: 'channel',
    loadComponent: () => import('../features/channel/channel').then(m => m.ChannelComponent),
  },
  {
    path: 'folder',
    loadComponent: () => import('../features/folder/folder').then(m => m.FolderComponent),
  },
  {
    path: 'collection',
    loadComponent: () => import('../features/collection/collection').then(m => m.CollectionComponent),
  },
  {
    path: 'bucket-file',
    loadComponent: () => import('../features/bucket-file/bucket-file').then(m => m.BucketFileComponent),
  },
  {
    path: 'chapter',
    loadComponent: () => import('../features/chapter/chapter').then(m => m.ChapterComponent),
  },
  {
    path: 'ebook',
    loadComponent: () => import('../features/ebook/ebook').then(m => m.EbookComponent),
  },
  {
    path: 'album',
    loadComponent: () => import('../features/album/album').then(m => m.AlbumComponent),
  },
  {
    path: 'article',
    loadComponent: () => import('../features/article/article').then(m => m.ArticleComponent),
  },
  {
    path: 'article-detail/:id',
    loadComponent: () => import('../features/article-detail/article-detail').then(m => m.ArticleDetailComponent),
  },
];
