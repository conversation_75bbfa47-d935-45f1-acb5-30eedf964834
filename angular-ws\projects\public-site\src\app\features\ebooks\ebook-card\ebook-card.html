<div class="flex flex-col p-6">
  <!-- 卡片网格容器 -->
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
    @for (item of cardItems(); track item.id) {
    <p-card
      styleClass="card-item"
      [id]="'card-' + item.id"
    >
      <ng-template pTemplate="header">
        <img
          class="rounded-t-xl w-56"
          [src]="item.thumbnailUrl"
          [alt]="item.title + ' 封面图片'"
        />
      </ng-template>
      <ng-template pTemplate="content">
        <div class="flex cursor-pointer">
          <h3 class="text-lg font-semibold mb-2">{{ item.title }}</h3>
        </div>
      </ng-template>
      <ng-template pTemplate="footer">
        <div class="flex justify-between items-center">
          <p-button size="small" [outlined]="true" label="阅读" icon="pi pi-book" (click)="openBook(item.id)"></p-button>
          <p-button size="small" [outlined]="true" label="听书" icon="pi pi-bookmark"></p-button>
        </div>
      </ng-template>
    </p-card>
    }
  </div>

  <!-- 分页组件 -->
  <!-- <div class="pagination-container">
    <p-paginator
      [first]="first"
      [rows]="rows"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>
  </div> -->
</div>
