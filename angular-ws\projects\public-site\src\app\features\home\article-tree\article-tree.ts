import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { OrderListModule } from 'primeng/orderlist';
import { TreeModule } from 'primeng/tree';
import { TreeNode } from 'primeng/api';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ActivatedRoute, Router } from '@angular/router';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import {
  ArticleAggregateResult,
  ArticleFileAggregateResult,
} from '@/proxy/holy-bless/results';
import { MediaType } from '@/proxy/holy-bless/enums';
import { GalleriaModule } from 'primeng/galleria';
import { I18nService } from '@/services/i18n.service';
import { skip, Subscription } from 'rxjs';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { LoadingService } from '@/services/loading.service';
import { PlayDownloadComponent } from '@/components/play-download/play-download';
import { MobileService } from '@/services/mobile.service';
import { CatalogComponent } from '@/components/catalog/catalog';
import { MobileCategoryFilesDrawerComponent } from '@/components/mobile-catagory-files-drawer/mobile-catagory-files-drawer';

@Component({
  selector: 'app-artical-tree',
  standalone: true,
  imports: [
    CommonModule,
    TreeModule,
    GalleriaModule,
    PlayDownloadComponent,
    MobileCategoryFilesDrawerComponent,
  ],
  templateUrl: './article-tree.html',
  styleUrls: ['./article-tree.scss'],
})
export class ArticleTreeComponent {
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  #route = inject(ActivatedRoute);
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);
  router = inject(Router);
  mobileService = inject(MobileService);

  leftDrawerVisible = signal(false);

  collectionId: number | null = null;
  files: TreeNode[] = [];

  articleDetail = signal<ArticleAggregateResult | null>(null);
  articleFiles = computed(() => this.articleDetail()?.articleFiles || []);
  primaryArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.isPrimary === true),
  );
  imageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType === MediaType.Image),
  );
  notImageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType !== MediaType.Image),
  );

  constructor() {}

  selectedFile!: TreeNode;
  ngOnInit() {
    this.#route.params.subscribe((params) => {
      if (!params['collectionId']) return;
      this.collectionId = +params['collectionId'];
      this.changeLanguage(this.collectionId);
      this.loadCollectionSummary();
    });
  }

  changeLanguage(collectionId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe(() => {
      this.#ReadOnlyCollectionService
        .getLanguageMatchingCollection(collectionId)
        .subscribe({
          next: (collection) => {
            if (!collection) {
              this.router.navigateByUrl(`/landing`);
              return;
            }
            if (collection.id !== collectionId) {
              this.router.navigateByUrl(`/home/<USER>/${collection.id}`);
            }
          },
        });
    });
    this.subs.add(sub);
  }

  loadCollectionSummary() {
    if (!this.collectionId) return;
    this.articleDetail.set(null);
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionArticleTitles(this.collectionId)
      .subscribe({
        next: (data) => {
          this.loadingService.hide();
          if (!data || data.length === 0) {
            this.router.navigateByUrl('/landing');
            return;
          }
          this.files = data.map((item) => this.buildTreeNode(item));
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  buildTreeNode(node: any): TreeNode {
    return {
      key: node.id,
      label: node.name || node.title,
      data: node,
      children: node.articles
        ? node.articles.map((article: any) => this.buildTreeNode(article))
        : [],
    };
  }

  loadArticalDetail(node: TreeNode) {
    if (!node.key) return;
    this.loadingService.show();
    this.leftDrawerVisible.set(false);
    this.#ReadOnlyArticleService.getArticleAggregate(+node.key).subscribe({
      next: (data) => {
        this.articleDetail.set(data);
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('获取文章详情失败:', error);
        this.loadingService.hide();
      },
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
