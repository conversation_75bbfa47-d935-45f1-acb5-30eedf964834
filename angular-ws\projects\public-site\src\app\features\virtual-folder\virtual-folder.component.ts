import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-virtual-folder',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: ` <router-outlet></router-outlet> `,
  styles: [
    `
      :host {
        flex: 1;
        display: flex;
      }
    `,
  ],
})
export class VirtualFolderComponent {
  openFolder(folderId: string) {
    console.log('Opening folder:', folderId);
  }
}
