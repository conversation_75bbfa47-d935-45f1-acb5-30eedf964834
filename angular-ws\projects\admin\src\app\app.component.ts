import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { MenuModule } from 'primeng/menu';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MenuModule, AvatarModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = 'admin';

  items: MenuItem[] = [
    {
      label: 'BucketFile',
      icon: 'pi pi-file',
    },
    {
      label: 'Folder & File',
      icon: 'pi pi-folder',
    },
    {
      label: 'Article',
      icon: 'pi pi-file-pdf',
    },
    {
      label: 'Ebook',
      icon: 'pi pi-book',
    },
    {
      label: 'Chapter',
      icon: 'pi pi-align-left',
    },
    {
      label: 'Album',
      icon: 'pi pi-tiktok',
    },
    {
      label: 'Collection',
      icon: 'pi pi-database',
    },
    {
      label: 'Channel',
      icon: 'pi pi-objects-column',
    },
  ];
}
