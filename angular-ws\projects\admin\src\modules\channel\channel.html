<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="justifyOptions"
      [(ngModel)]="value"
      optionLabel="label"
      optionValue="label"
    >
    </p-selectbutton>
    <p-button label="Create"></p-button>
  </div>
  <p-treetable
    #tt
    [value]="files()"
    [columns]="cols"
    dataKey="key"
    [scrollable]="true"
    [tableStyle]="{ 'min-width': '50rem' }"
    filterMode="strict"
  >
    <ng-template #header let-columns>
      <tr>
        <th class="w-[4rem]"></th>
        @for (col of columns; track $index) {
        <th>{{ col.header }}</th>
        }
        <th></th>
      </tr>
      <tr>
        <th class="w-[4rem]"></th>
        @for (col of columns; track $index) {
        <th>
          <input
            class="w-full"
            pInputText
            [placeholder]="col.field"
            type="text"
            (input)="onFilter($event, col.field, col.filterMatchMode)"
          />
        </th>
        }
        <th></th>
      </tr>
    </ng-template>
    <ng-template #body let-rowNode let-rowData="rowData" let-columns="columns">
      <tr
        [ttRow]="rowNode"
        (dragover)="onDragOver($event, rowData)"
        (drop)="onDrop($event)"
        (dragenter)="onDragEnter($event, rowData)"
        (dragleave)="onDragLeave($event)"
        [class.drag-over-top]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'top'"
        [class.drag-over-middle]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'middle'"
        [class.drag-over-bottom]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'bottom'"
      >
        <td class="w-[4rem]">
          <span
            class="cursor-move"
            draggable="true"
            (dragstart)="onDragStart($event, rowData)"
          >
            <svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
              <path
                d="M2 4h2v2H2V4zm0 4h2v2H2V8zm0 4h2v2H2v-2zm4-8h2v2H6V4zm0 4h2v2H6V8zm0 4h2v2H6v-2zm4-8h2v2h-2V4zm0 4h2v2h-2V8zm0 4h2v2h-2v-2z"
              />
            </svg>
          </span>
        </td>
        @for (col of columns; track $index) {
        <td>
          @if($index===0) {
          <p-treetable-toggler [rowNode]="rowNode" />
          <span
            [class.drag-over-top]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'top'"
            [class.drag-over-middle]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'middle'"
            [class.drag-over-bottom]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'bottom'"
          >
            {{ rowData[col.field] }}
          </span>
          } @else{ {{ rowData[col.field] }}}
        </td>
        }
        <td>
          <p-button
            icon="pi pi-pencil"
            [text]="true"
            styleClass="!rounded-full"
            (onClick)="selectChannel.set(rowData)"
          ></p-button>
          <p-button
            icon="pi pi-trash"
            [text]="true"
            styleClass="!rounded-full !text-red-500"
          ></p-button>
        </td>
      </tr>
    </ng-template>
  </p-treetable>
</div>

<p-dialog
  header="Edit Channel"
  [modal]="true"
  [(visible)]="selectChannel"
  [style]="{ width: '25rem' }"
>
  @if(selectChannel()) {
  <div class="form">
    <div class="form-item">
      <label>Title</label>
      <input type="text" pInputText [(ngModel)]="selectChannel().title" />
    </div>
    <div class="form-item">
      <label>Content code</label>
      <input type="text" pInputText [(ngModel)]="selectChannel().contentCode" />
    </div>
    <div class="form-item">
      <label>Channel source</label>
      <p-select
        [options]="ChannelTypes"
        [(ngModel)]="selectChannel().channelSource"
        optionLabel="label"
        optionValue="value"
        placeholder="Select a channel source"
        appendTo="body"
      />
    </div>
  </div>
  }
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button styleClass="w-full" severity="secondary">Cancel</p-button>
    <p-button styleClass="w-full" >Confirm</p-button>
  </div>
</p-dialog>
