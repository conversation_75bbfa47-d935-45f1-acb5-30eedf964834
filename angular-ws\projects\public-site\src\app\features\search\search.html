<p-tabs [(value)]="activeTab" (valueChange)="dataList = []">
  <p-tablist>
    <p-tab [value]="0">{{ i18nService.translate('article') }}</p-tab>
    <p-tab [value]="1">{{ i18nService.translate('file') }}</p-tab>
  </p-tablist>
  <p-tabpanels>
    <p-tabpanel [value]="0">
      <div class="w-[20rem]">
        <div>
          <p class="mb-2">{{ i18nService.translate('keyword') }}</p>
          <input type="text" pInputText [(ngModel)]="keyword" class="w-full" />
        </div>
        <div class="mt-4">
          <p class="mb-2">{{ i18nService.translate('searchScope') }}</p>
          <div class="flex items-center gap-4">
            <div class="flex items-center">
              <p-checkbox
                class="flex items-center"
                inputId="ingredient1"
                name="searchFields"
                [value]="0"
                [(ngModel)]="searchFields"
              />
              <label for="ingredient1" class="ml-2"
                >{{ i18nService.translate('title') }}</label
              >
            </div>
            <div class="flex items-center">
              <p-checkbox
                class="flex items-center"
                inputId="ingredient2"
                name="searchFields"
                [value]="1"
                [(ngModel)]="searchFields"
              />
              <label for="ingredient2" class="ml-2"
                >{{ i18nService.translate('content') }}</label
              >
            </div>
          </div>
        </div>
        <div class="mt-4">
          <p class="mb-2">{{ i18nService.translate('articleType') }}</p>
          <p-multiselect
            [options]="articleContentCategoryOptions()"
            optionLabel="name"
            [placeholder]="i18nService.translate('All')"
            class="w-full"
            [(ngModel)]="articleContentCategories"
          />
        </div>
        <div class="mt-4">
          <p class="mb-2">{{ i18nService.translate('deliveryDate') }}</p>
          <div class="flex items-center gap-4">
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateStart"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
            <p-datepicker [iconDisplay]="'input'" [showIcon]="true">
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
          </div>
        </div>
        <div class="mt-4">
          <p-button
            [label]="i18nService.translate('search')"
            (onClick)="searchArticles()"
          ></p-button>
        </div>
      </div>
    </p-tabpanel>
    <p-tabpanel [value]="1">
      <div class="w-[20rem]">
        <div>
          <p class="mb-2">{{ i18nService.translate('fileName') }}</p>
          <input type="text" pInputText [(ngModel)]="keyword" class="w-full" />
        </div>
        <div class="mt-4">
          <p class="mb-2">{{ i18nService.translate('contentType') }}</p>
          <p-multiselect
            [options]="contentCategoryOptions()"
            [(ngModel)]="fileContentCategories"
            optionLabel="name"
            [placeholder]="i18nService.translate('all')"
            class="w-full"
          />
        </div>
        <div class="mt-4">
          <p class="mb-2">{{ i18nService.translate('deliveryDate') }}</p>
          <div class="flex items-center gap-4">
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateStart"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
            <p-datepicker
              [iconDisplay]="'input'"
              [showIcon]="true"
              [(ngModel)]="deliveryDateEnd"
            >
              <ng-template #inputicon let-clickCallBack="clickCallBack">
                <i class="pi pi-clock" (click)="clickCallBack($event)"></i>
              </ng-template>
            </p-datepicker>
          </div>
        </div>
        <div class="mt-4">
          <p-button
            [label]="i18nService.translate('search')"
            (onClick)="searchFiles()"
          ></p-button>
        </div>
      </div>
    </p-tabpanel>
  </p-tabpanels>
</p-tabs>
<div class="flex-1">
  @if ( activeTab !== 0 ) {
  <div class="flex items-center gap-2">
    <p-button
      icon="pi pi-cloud-download"
      [label]="i18nService.translate('download')"
      [outlined]="true"
      (onClick)="downloadService.batchDownloadAsZip(selectedProducts)"
    />
    <p-button
      icon="pi pi-play-circle"
      [label]="i18nService.translate('play')"
      [outlined]="true"
      (onClick)="playerService.multiPlayVideo(selectedProducts)"
    />
  </div>
  } @if(dataList && dataList.length > 0) {
  <p-table
    [value]="dataList"
    [paginator]="true"
    [rows]="rows()"
    [first]="first()"
    [totalRecords]="totalRecords()"
    (onPage)="onPageChange($event)"
    size="small"
    [lazy]="true"
    stripedRows
    class="w-full"
    [(selection)]="selectedProducts"
  >
    <ng-template #header>
      <tr>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th>{{ i18nService.translate('name') }}</th>
        @if ( activeTab !== 0 ) {
        <th>{{ i18nService.translate('contentType') }}</th>
        }
        <th style="width: 12rem">
          {{ i18nService.translate('lastModifiedTime') }}
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-product>
      <tr>
        <td>
          <p-tableCheckbox [value]="product" />
        </td>
        <td>{{ (product.title || product.fileName) | removeExtension }}</td>
        @if ( activeTab !== 0 ) {
        <td>
          {{
          i18nService.translate(contentCategoryMap()[product.contentCategory])
          }}
        </td>
        }
        <td>
          {{ product.lastModificationTime | date: "yyyy-MM-dd" }}
        </td>
      </tr>
    </ng-template>
  </p-table>
  }
</div>
