<p-table [value]="data">
  <ng-template pTemplate="header">
    <tr>
      <th style="width: 4rem"></th>
      <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
      @for (item of columns; track $index) {
      <th>{{ item.header }}</th>
      }
      <th style="width: 8rem"></th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-rowData>
    <tr
      (dragover)="handleDragOver($event, rowData)"
      (drop)="handleDrop($event)"
      (dragenter)="handleDragEnter($event, rowData)"
      (dragleave)="handleDragLeave($event)"
      [class.drag-over-top]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'top'"
      [class.drag-over-middle]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'middle'"
      [class.drag-over-bottom]="isDragging() && dropRow() && dropRow()!.id === rowData.id && dropPosition() === 'bottom'"
    >
      <td class="w-[4rem]">
        <span
          class="cursor-move"
          draggable="true"
          (dragstart)="handleDragStart($event, rowData)"
        >
          <svg width="20" height="20" viewBox="0 0 16 16" fill="currentColor">
            <path
              d="M2 4h2v2H2V4zm0 4h2v2H2V8zm0 4h2v2H2v-2zm4-8h2v2H6V4zm0 4h2v2H6V8zm0 4h2v2H6v-2zm4-8h2v2h-2V4zm0 4h2v2h-2V8zm0 4h2v2h-2v-2z"
            />
          </svg>
        </span>
      </td>
      <td>
        <p-tableCheckbox [value]="rowData" />
      </td>
      @for (item of columns; track $index) {
      <td>{{ rowData[item.field] }}</td>
      }
      <td>
        <p-button
          [text]="true"
          styleClass="!rounded-full"
          icon="pi pi-pencil"
        ></p-button>
        <p-button
          [text]="true"
          styleClass="!rounded-full"
          icon="pi pi-trash"
          severity="danger"
        ></p-button>
      </td>
    </tr>
  </ng-template>
</p-table>
