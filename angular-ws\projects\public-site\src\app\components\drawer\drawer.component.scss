.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s ease;

  &.show {
    opacity: 1;
  }
}

.drawer-container {
  position: fixed;
  z-index: 1001;
  background: #565656;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.drawer {
    &-left {
      top: 0;
      left: 0;
      height: 100vh;
      border-right: 1px solid #e5e7eb;
      transform: translateX(-100%);
    }

    &-right {
      top: 0;
      right: 0;
      height: 100vh;
      border-left: 1px solid #e5e7eb;
      transform: translateX(100%);
    }

    &-top {
      top: 0;
      left: 0;
      width: 100vw;
      border-bottom: 1px solid #e5e7eb;
      transform: translateY(-100%);
    }

    &-bottom {
      bottom: 0;
      left: 0;
      width: 100vw;
      border-top: 1px solid #e5e7eb;
      transform: translateY(100%);
    }

    // 尺寸
    &-small {
      &.drawer-left,
      &.drawer-right {
        width: 20rem;
      }
      &.drawer-top,
      &.drawer-bottom {
        height: 20rem;
      }
    }

    &-medium {
      &.drawer-left,
      &.drawer-right {
        width: 26rem;
      }
      &.drawer-top,
      &.drawer-bottom {
        height: 26rem;
      }
    }

    &-large {
      &.drawer-left,
      &.drawer-right {
        width: 36rem;
      }
      &.drawer-top,
      &.drawer-bottom {
        height: 36rem;
      }
    }

    &-full {
      &.drawer-left,
      &.drawer-right {
        width: 100vw;
      }
      &.drawer-top,
      &.drawer-bottom {
        height: 100vh;
      }
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 1.5rem;
  background: #565656;
  flex-shrink: 0;

  .drawer-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
  }

  .drawer-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: none;
    background: transparent;
    border-radius: 0.375rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.15s ease;

    &:hover {
      background: #e5e7eb;
      color: #374151;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px #3b82f6;
    }
  }
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.drawer-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .drawer-container {
    &.drawer-left,
    &.drawer-right {
      width: 100vw !important;
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .drawer-container {
    background: #1f2937;
    color: #f9fafb;

    .drawer-header {
      background: #374151;
      border-bottom-color: #4b5563;

      .drawer-title {
        color: #f9fafb;
      }

      .drawer-close-btn {
        color: #d1d5db;

        &:hover {
          background: #4b5563;
          color: #f9fafb;
        }
      }
    }

    .drawer-footer {
      background: #374151;
      border-top-color: #4b5563;
    }
  }
}