import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class MobileService {
  #isMobile = new BehaviorSubject<boolean>(false);
  isMobile = this.#isMobile.asObservable();

  constructor() {
    this.#detectMobile();
    window.addEventListener('resize', () => this.#detectMobile());
  }

  #detectMobile() {
    this.#isMobile.next(window.innerWidth <= 768);
  }
}
