<div class="p-4">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <div>
      <p-button
        [label]="i18nService.t()('Create')"
        [text]="true"
        (onClick)="handleCreate()"
      ></p-button>
    </div>
  </div>
  <c-tree-table
    [data]="data"
    [cols]="cols()"
    [expandAll]="expandAll"
    parentKey="parentCollectionId"
    (onDrop)="handleDrop($event)"
    (onEdit)="handleEdit($event)"
    (onAdd)="handleAdd($event)"
    (onDelete)="handleDelete($event)"
  ></c-tree-table>
</div>

<p-drawer
  [(visible)]="drawerVisible"
  position="right"
  [style]="{ width: '40rem' }"
>
  <p-table
    [value]="subData()"
    [reorderableColumns]="true"
    [(selection)]="selectSub"
    stripedRows
  >
    <ng-template #header>
      <tr>
        <th style="width: 4rem"></th>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        <th style="width: 4rem">{{i18nService.t()('ID')}}</th>
        <th>{{i18nService.t()('Title')}}</th>
        <th>
          <p-button
            [text]="true"
            icon="pi pi-trash"
            [label]="i18nService.t()('Remove')"
            severity="danger"
            (onClick)="handleRemove()"
          ></p-button>
        </th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr [pReorderableRow]="index">
        <td>
          <span class="pi pi-bars" pReorderableRowHandle></span>
        </td>
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        <td>{{rowData.id}}</td>
        <td>{{rowData.title}}</td>
        <td></td>
      </tr>
    </ng-template>
  </p-table>
</p-drawer>

<p-dialog
  [header]="(mode() === 'edit') ? (i18nService.t()('Edit')) : (i18nService.t()('Create'))"
  [modal]="true"
  [(visible)]="selectData"
  [style]="{ width: '50vw' }"
>
  @if(selectData()) {
  <div class="form">
    <div class="form-item">
      <label> {{i18nService.t()('Name')}}: </label>
      <input type="text" pInputText [(ngModel)]="selectData().name" />
    </div>
    <div class="form-item">
      <label> {{i18nService.t()('ParentCollection')}}: </label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectData().parentCollection"
        [options]="collectionTree()"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Channel')}}:</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectData().channel"
        [options]="channelTree()"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('ListStyle')}}:</label>
      <p-select
        [options]="listStyleOptions"
        [(ngModel)]="selectData().listStyle"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Keywords')}}:</label>
      <input type="text" pInputText [(ngModel)]="selectData().keywords" />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('ContentCode')}}:</label>
      <input type="text" pInputText [(ngModel)]="selectData().contentCode" />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Description')}}:</label>
      <input type="text" pInputText [(ngModel)]="selectData().description" />
    </div>
  </div>
  }
  <ng-template #footer>
    <div class="w-full grid grid-cols-2 gap-2 mt-4">
      <p-button
        styleClass="w-full"
        severity="secondary"
        [label]="i18nService.t()('Cancel')"
        (onClick)="selectData.set(null)"
      ></p-button>
      <p-button
        styleClass="w-full"
        (onClick)="updateSelectChannel()"
        [label]="i18nService.t()('Confirm')"
      >
      </p-button>
    </div>
  </ng-template>
</p-dialog>
