import type { CollectionArticleTreeDto, CollectionDto, CollectionTreeDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import { Injectable } from '@angular/core';
import type { ArticleTitleDto } from '../articles/dtos/models';
import type { CollectionSummaryRequest, CollectionSummaryResult } from '../results/models';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyCollectionService {
  apiName = 'Default';
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/read-only-collection/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticleTitles = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleTitleDto[]>({
      method: 'GET',
      url: `/api/app/read-only-collection/collection-article-titles/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionArticleTitlesByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ArticleTitleDto[]>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-article-titles-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getCollectionSummary = (collectionId: number, request: CollectionSummaryRequest, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionSummaryResult>({
      method: 'GET',
      url: `/api/app/read-only-collection/collection-summary/${collectionId}`,
      params: { skip: request.skip, maxResultCount: request.maxResultCount, sorting: request.sorting, year: request.year, month: request.month },
    },
    { apiName: this.apiName,...config });
  

  getCollectionSummaryByContentCode = (contentCode: string, request: CollectionSummaryRequest, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionSummaryResult>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-summary-by-content-code',
      params: { contentCode, skip: request.skip, maxResultCount: request.maxResultCount, sorting: request.sorting, year: request.year, month: request.month },
    },
    { apiName: this.apiName,...config });
  

  getCollectionTree = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionTreeDto[]>({
      method: 'GET',
      url: `/api/app/read-only-collection/collection-tree/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeAndArticleTitles = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionArticleTreeDto[]>({
      method: 'GET',
      url: `/api/app/read-only-collection/collection-tree-and-article-titles/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeAndArticleTitlesByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionArticleTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-tree-and-article-titles-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getCollectionTreeByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-collection/collection-tree-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getFirstByChannelId = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/read-only-collection/first-by-channel-id/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getLanguageMatchingCollection = (collectionId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: `/api/app/read-only-collection/language-matching-collection/${collectionId}`,
    },
    { apiName: this.apiName,...config });
  

  getLanguageMatchingCollectionByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, CollectionDto>({
      method: 'GET',
      url: '/api/app/read-only-collection/language-matching-collection-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
