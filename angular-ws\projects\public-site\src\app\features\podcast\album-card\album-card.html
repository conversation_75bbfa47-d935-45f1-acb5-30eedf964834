<div class="flex flex-col p-6">
  <!-- 卡片网格容器 -->
  <div class="collection-header">
    <h3 class="collection-title">{{ i18nService.translate('audio') }}</h3>
  </div>
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
    @for (item of audioItems(); track item.id) {
    <p-card styleClass="card-item" [id]="'card-' + item.id">
      <ng-template pTemplate="header">
        <img
          class="rounded-t-[3px] w-56"
          [src]="item.thumbnailUrl"
          [alt]="item.title + ' 封面图片'"
        />
      </ng-template>
      <p class="p-card-subtitle text-sm">
        {{ "2023-01-01" | date: 'yyyy-MM-dd' }}
      </p>
      <p class="p-card-title mt-2 cursor-pointer" (click)="openBook(item.id)">
        {{ item.title }}
      </p>
    </p-card>
    }
  </div>
  <div class="pagination-container">
    <p-paginator
      [first]="audioFirst"
      [rows]="audioRows"
      [totalRecords]="audioTotalRecords"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [showPageLinks]="!isMobile"
      [showCurrentPageReport]="isMobile"
      (onPageChange)="onAudioPageChange($event)"
      styleClass="custom-paginator"
    >
    </p-paginator>

    <!-- 卡片网格容器 -->
    <div class="collection-header">
      <h3 class="collection-title">{{ i18nService.translate('video') }}</h3>
    </div>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
      @for (item of videoItems(); track item.id) {
      <p-card styleClass="card-item" [id]="'card-' + item.id">
        <ng-template pTemplate="header">
          <img
            class="rounded-t-[3px] w-56"
            [src]="item.thumbnailUrl"
            [alt]="item.title + ' 封面图片'"
          />
        </ng-template>
        <p class="p-card-subtitle text-sm">
          {{ "2023-01-01" | date: 'yyyy-MM-dd' }}
        </p>
        <p class="p-card-title mt-2 cursor-pointer" (click)="openBook(item.id)">
          {{ item.title }}
        </p>
      </p-card>
      }
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <p-paginator
        [first]="videoFirst"
        [rows]="videoRows"
        [totalRecords]="videoTotalRecords"
        [rowsPerPageOptions]="rowsPerPageOptions"
        [showPageLinks]="!isMobile"
        [showCurrentPageReport]="isMobile"
        (onPageChange)="onVideoPageChange($event)"
        styleClass="custom-paginator"
      >
      </p-paginator>
    </div>
  </div>
</div>
