import Dexie, { Table } from 'dexie';
import { Injectable } from '@angular/core';

interface CachedVideo {
  id?: number;
  url: string;
  blob: Blob;
  fileName: string;
  cachedAt: number;
  size: number;
}

class VideoCacheDB extends Dexie {
  videos!: Table<CachedVideo, number>;

  constructor() {
    super('VideoCacheDB');
    this.version(1).stores({
      videos: '++id, url, fileName, cachedAt, size'
    });
  }
}

@Injectable({
  providedIn: 'root'
})
export class VideoCacheService {
  private db = new VideoCacheDB();

  async cacheVideo(videoUrl: string, fileName: string): Promise<string> {
    try {
      // 检查是否已缓存
      const cached = await this.getCachedVideo(videoUrl);
      if (cached) {
        return URL.createObjectURL(cached.blob);
      }

      // 尝试下载视频
      const blob = await this.downloadVideo(videoUrl);
      if (blob) {
        // 保存到数据库
        await this.db.videos.add({
          url: videoUrl,
          blob: blob,
          fileName: fileName,
          cachedAt: Date.now(),
          size: blob.size
        });

        return URL.createObjectURL(blob);
      } else {
        // 如果无法下载，直接返回原始 URL
        return videoUrl;
      }
    } catch (error) {
      console.error('Error caching video:', error);
      // 降级处理：返回原始 URL
      return videoUrl;
    }
  }

  private async downloadVideo(videoUrl: string): Promise<Blob | null> {
    try {
      // 尝试不同的下载策略
      
      // 策略1: 直接 fetch
      const response = await fetch(videoUrl, {
        mode: 'cors',
        credentials: 'omit'
      });
      
      if (response.ok) {
        return await response.blob();
      }
    } catch (error) {
      console.warn('Direct fetch failed, trying alternative methods:', error);
    }

    try {
      // 策略2: 使用 no-cors 模式 (有限制，但可能绕过某些 CORS 问题)
      const response = await fetch(videoUrl, {
        mode: 'no-cors'
      });
      
      if (response.type === 'opaque') {
        // no-cors 模式下无法读取响应内容，只能返回 null
        console.warn('Video is behind CORS, cannot cache');
        return null;
      }
    } catch (error) {
      console.warn('No-cors fetch failed:', error);
    }

    try {
      // 策略3: 使用 XMLHttpRequest (某些情况下可能有不同的行为)
      return await this.downloadWithXHR(videoUrl);
    } catch (error) {
      console.warn('XHR download failed:', error);
    }

    return null;
  }

  private downloadWithXHR(url: string): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.responseType = 'blob';
      xhr.withCredentials = false;

      xhr.onload = () => {
        if (xhr.status === 200) {
          resolve(xhr.response);
        } else {
          reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error('Network error'));
      };

      xhr.send();
    });
  }

  // 检查 URL 是否可以被缓存
  async canCacheUrl(videoUrl: string): Promise<boolean> {
    try {
      const response = await fetch(videoUrl, {
        method: 'HEAD',
        mode: 'cors',
        credentials: 'omit'
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // 智能缓存：只缓存可以访问的视频
  async smartCacheVideo(videoUrl: string, fileName: string): Promise<string> {
    try {
      // 检查是否已缓存
      const cached = await this.getCachedVideo(videoUrl);
      if (cached) {
        return URL.createObjectURL(cached.blob);
      }

      // 检查是否可以缓存
      const canCache = await this.canCacheUrl(videoUrl);
      if (!canCache) {
        console.log('Video cannot be cached due to CORS restrictions:', videoUrl);
        return videoUrl; // 直接返回原始 URL
      }

      // 可以缓存，则进行缓存
      return await this.cacheVideo(videoUrl, fileName);
    } catch (error) {
      console.error('Error in smart cache:', error);
      return videoUrl;
    }
  }

  async getCachedVideo(videoUrl: string): Promise<CachedVideo | undefined> {
    const cached = await this.db.videos.where('url').equals(videoUrl).first();
    console.log('cached',cached)
    return cached;
  }

  async deleteCachedVideo(videoUrl: string): Promise<void> {
    await this.db.videos.where('url').equals(videoUrl).delete();
  }

  async getAllCachedVideos(): Promise<CachedVideo[]> {
    return await this.db.videos.toArray();
  }

  async clearCache(): Promise<void> {
    await this.db.videos.clear();
  }

  async getCacheSize(): Promise<number> {
    const videos = await this.db.videos.toArray();
    return videos.reduce((total, video) => total + video.size, 0);
  }

  // 删除过期缓存 (7天前的)
  async cleanOldCache(daysOld: number = 7): Promise<number> {
    const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    return await this.db.videos.where('cachedAt').below(cutoffTime).delete();
  }

  // 获取缓存统计信息
  async getCacheStats() {
    const videos = await this.getAllCachedVideos();
    const totalSize = await this.getCacheSize();
    const oldestCache = videos.length > 0 ? Math.min(...videos.map(v => v.cachedAt)) : null;
    
    return {
      totalVideos: videos.length,
      totalSize: totalSize,
      formattedSize: this.formatBytes(totalSize),
      oldestCacheDate: oldestCache ? new Date(oldestCache) : null
    };
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}