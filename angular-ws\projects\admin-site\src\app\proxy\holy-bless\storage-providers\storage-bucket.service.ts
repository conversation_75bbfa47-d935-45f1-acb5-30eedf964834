import type { CreateUpdateStorageBucketDto, StorageBucketDto } from './dtos/models';
import { RestService, Rest } from '@abp/ng.core';
import type { PagedAndSortedResultRequestDto, PagedResultDto } from '@abp/ng.core';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class StorageBucketService {
  apiName = 'Default';
  

  create = (input: CreateUpdateStorageBucketDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto>({
      method: 'POST',
      url: '/api/app/storage-bucket',
      body: input,
    },
    { apiName: this.apiName,...config });
  

  delete = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, void>({
      method: 'DELETE',
      url: `/api/app/storage-bucket/${id}`,
    },
    { apiName: this.apiName,...config });
  

  get = (id: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto>({
      method: 'GET',
      url: `/api/app/storage-bucket/${id}`,
    },
    { apiName: this.apiName,...config });
  

  getAllBuckets = (config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto[]>({
      method: 'GET',
      url: '/api/app/storage-bucket/buckets',
    },
    { apiName: this.apiName,...config });
  

  getBucketByName = (bucketName: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto>({
      method: 'GET',
      url: '/api/app/storage-bucket/bucket-by-name',
      params: { bucketName },
    },
    { apiName: this.apiName,...config });
  

  getBucketsByLanguage = (languageCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto[]>({
      method: 'GET',
      url: '/api/app/storage-bucket/buckets-by-language',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getBucketsByProviderId = (providerId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto[]>({
      method: 'GET',
      url: `/api/app/storage-bucket/buckets-by-provider-id/${providerId}`,
    },
    { apiName: this.apiName,...config });
  

  getList = (input: PagedAndSortedResultRequestDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, PagedResultDto<StorageBucketDto>>({
      method: 'GET',
      url: '/api/app/storage-bucket',
      params: { sorting: input.sorting, skipCount: input.skipCount, maxResultCount: input.maxResultCount },
    },
    { apiName: this.apiName,...config });
  

  getListByProviderIdAysncByProviderId = (providerId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto[]>({
      method: 'GET',
      url: `/api/app/storage-bucket/by-provider-id-aysnc/${providerId}`,
    },
    { apiName: this.apiName,...config });
  

  update = (id: number, input: CreateUpdateStorageBucketDto, config?: Partial<Rest.Config>) =>
    this.restService.request<any, StorageBucketDto>({
      method: 'PUT',
      url: `/api/app/storage-bucket/${id}`,
      body: input,
    },
    { apiName: this.apiName,...config });

  constructor(private restService: RestService) {}
}
