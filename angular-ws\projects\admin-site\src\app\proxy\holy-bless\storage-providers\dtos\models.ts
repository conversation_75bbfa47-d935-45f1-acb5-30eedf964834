import type { ContentCategory } from '../../enums/content-category.enum';
import type { EntityDto } from '@abp/ng.core';
import type { CountryDto } from '../../lookups/dtos/models';

export interface CreateUpdateProviderSecretDto {
  accessId: string;
  accessSecretKey: string;
  apiEndPoint: string;
  description?: string;
}

export interface CreateUpdateStorageBucketDto {
  storageProviderId: number;
  bucketName: string;
  languageCode: string;
  spokenLangCode?: string;
  subDomain: string;
  contentType?: ContentCategory;
}

export interface CreateUpdateStorageProviderDto {
  providerName: string;
  providerCode: string;
  preferCountryIds: number[];
  description?: string;
  bindedDomain?: string;
}

export interface ProviderSecretDto extends EntityDto<number> {
  accessId?: string;
  accessSecretKey?: string;
  apiEndPoint?: string;
  description?: string;
}

export interface StorageBucketDto extends EntityDto<number> {
  storageProviderId: number;
  storageName?: string;
  bucketName?: string;
  languageCode?: string;
  spokenLangCode?: string;
  subDomain?: string;
  contentType?: ContentCategory;
}

export interface StorageProviderDto extends EntityDto<number> {
  providerName?: string;
  providerCode?: string;
  preferCountries: CountryDto[];
  description?: string;
  bindedDomain?: string;
}
