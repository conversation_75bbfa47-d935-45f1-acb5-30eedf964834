<app-drawer
  class="player-drawer"
  [visible]="drawerService.playerVisible"
  position="right"
  [modal]="true"
  (onHide)="drawerService.closePlayer()"
>
  <i
    class="pi pi-times text-[#ccc] hover:text-[#fff] cursor-pointer p-2 rounded-full mb-2"
    (click)="drawerService.playerVisible.set(false)"
  ></i>
  <video
    #videoElement
    controls
    src=""
    class="bg-[#000] w-full h-[13.5rem]"
    autoplay
  ></video>
  <div class="flex justify-between items-center py-2">
    <div class="flex gap-4">
      <img src="assets/images/pre.svg" alt="Pre" />
      <img
        [src]=" isPlaying() ? 'assets/images/pause.svg' : 'assets/images/play.svg'"
        alt="Play"
        (click)="tooglePlay()"
      />
      <img src="assets/images/next.svg" alt="Next" (click)="playNext()" />
    </div>
    <div class="flex gap-2">
      <div
        class="flex items-center gap-2 relative hover:bg-[#333] rounded-full px-2 cursor-pointer transition duration-300"
      >
        <div
          class="volume-slider w-20 h-1 bg-[#ccc] rounded-full cursor-pointer relative"
          (click)="onVolumeTrackClick($event)"
          #volumeTrack
        >
          <!-- 音量填充条 -->
          <div
            class="volume-fill h-full bg-[#fff] rounded-full"
            [style.width.%]="volume()"
          ></div>

          <!-- 可拖拽的小球 -->
          <div
            class="volume-handle w-3 h-3 bg-[#fff] rounded-full absolute cursor-pointer"
            [style.left.%]="volume()"
            [style.transform]="'translateX(-50%) translateY(-50%)'"
            [style.top]="'50%'"
            (mousedown)="onVolumeHandleMouseDown($event)"
            #volumeHandle
          ></div>
        </div>
        <img
          #volumeButton
          src="assets/images/volume.svg"
          alt="音量"
          class="cursor-pointer"
        />
      </div>
      <div (click)="changePlayMode()" class="flex items-center">
        <img
          [src]="playMode() === 'loop' ? 'assets/images/loop.svg' : playMode() === 'single' ? 'assets/images/single.svg' : 'assets/images/random.svg'"
          alt=""
        />
      </div>
    </div>
  </div>
  <div>
    @if (drawerService.playerVisible()) {
    <app-lyric-scroll [currentTime]="currentTime"></app-lyric-scroll>
    }
  </div>
  <div class="text-[#fff]">
    <ul>
      <li class="flex items-center justify-between mt-6 mb-2">
        <span>Play List</span>
        <img src="assets/images/delete.svg" alt="" (click)="playerService.clearPlayerList()" />
      </li>
      @for (item of playerService.playerList$ | async; track $index) {
      <li
        class="flex items-center justify-between py-2 text-[#ccc] hover:text-[#fff] cursor-pointer"
        [class.active]="activeItem().fileId === item.fileId"
      >
        @if (activeItem().fileId === item.fileId) {
        <i class="pi pi-volume-up mr-2"></i>
        }
        <span
          class="truncate flex-1 mr-4 cursor-pointer"
          (click)="playVideo(item)"
        >
          {{item.fileName | removeExtension }}
        </span>
        <i class="pi pi-times flex-shrink-0" (click)="playerService.removeFromPlayerList(item)"></i>
      </li>
      }
    </ul>
  </div>
</app-drawer>
