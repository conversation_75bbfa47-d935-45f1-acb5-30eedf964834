import { Routes } from '@angular/router';

export const HOME_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./home.component').then((m) => m.HomeComponent),
    title: 'HolyBless - 主站',
    children: [
      {
        path: 'image-cards/:collectionId',
        loadComponent: () =>
          import('./image-cards/image-cards.component').then(
            (m) => m.ImageCardsComponent,
          ),
        title: 'HolyBless - 有封面列表',
      },
      {
        path: 'summary-cards/:collectionId',
        loadComponent: () =>
          import('./summary-cards/summary-cards.component').then(
            (m) => m.SummaryCardsComponent,
          ),
        title: 'HolyBless - 摘要卡片列表',
      },
      {
        path: 'article-detail/:articleId',
        loadComponent: () =>
          import('./article-detail/article-detail').then(
            (m) => m.ArticleDetailComponent,
          ),
        title: 'HolyBless - 文章详情',
      },
      {
        path: 'article-tree/:collectionId',
        loadComponent: () =>
          import('./article-tree/article-tree').then(
            (m) => m.ArticleTreeComponent,
          ),
        title: 'HolyBless - 文章树',
      },
      {
        path: 'collection-article-tree/:collectionId',
        loadComponent: () =>
          import('./collection-article-tree/collection-article-tree').then(
            (m) => m.CollectionArticleTreeComponent,
          ),
        title: 'HolyBless - 收藏文章树',
      },
      {
        path: 'collection-tree/:collectionId',
        loadComponent: () =>
          import('./collection-tree/collection-tree').then(
            (m) => m.CollectionTreeComponent,
          ),
        title: 'HolyBless - 收藏树',
      },
    ],
  },
];
