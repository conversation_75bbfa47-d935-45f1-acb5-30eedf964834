/* You can add global styles to this file, and also import other style files */
@import "./tailwind.css";
@import "primeicons/primeicons.css";

.app-default {
  --bannerbg: #fff5eb;
  --bannertext: #656363;
  --contentbg: #ffffff;
  --contenttext: #656363;
  --subcontenttext: #6b7280;
  --buttonborder: #d1d5db;
  --buttontext: #6b7280;
  --scrollbar-track: #fcfcfc;
  --scrollbar-thumb: #8b8b8b;
  --table-striped: #f9f9f9;
  --folder-download-bg: #fff;
}

.app-green {
  --bannerbg: #c9e0cb;
  --bannertext: #516d58;
  --contentbg: #dce9dd;
  --contenttext: #656363;
  --subcontenttext: #6b7280;
  --buttonborder: #d1d5db;
  --buttontext: #6b7280;
  --scrollbar-track: #fcfcfc;
  --scrollbar-thumb: #8b8b8b;
  --table-striped: #f9f9f9;
  --folder-download-bg: #fff;
}
.app-dark {
  --bannerbg: #121212;
  --scrollbar-track: #2c2c2c;
  --scrollbar-thumb: #9f9f9f;
  --scrollbar-thumb-hover: #d1d1d1;
  --table-striped: #2c2c2c;
  --folder-download-bg: #000;
}

body {
  color: var(--contenttext);
}
.app-container {
  background: var(--contentbg);
}
.p-menubar {
  background: var(--bannerbg) !important;
  border: unset;
}
.p-card {
  height: 100%;
}
.p-card {
  height: 100%;
}

.p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(odd) {
  background-color: var(--table-striped) !important;
}

.p-datatable-striped.p-datatable-hoverable
  .p-datatable-tbody
  > tr:not(.p-datatable-row-selected):hover {
  background: var(--p-datatable-row-hover-background) !important;
}

.p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
  padding: 0.25rem 0.5rem !important;
  height: 50px;
}

.folder-download {
  @apply hover:bg-[var(--folder-download-bg)] p-2 rounded-full;
}

.p-card {
  border-radius: 3px !important;
}

/* 主题色滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 10px;
}

@media screen and (max-width: 768px) {
  .p-card-body {
    padding: 0.5rem !important;
  }
}
.main-content-text {
  @apply text-[1.25rem] leading-[1.75em];
}

.p-card-body {
  @apply !p-3;
}

.p-datatable-thead > tr > th, .p-datatable-tbody > tr > td {
  border: unset !important;
}