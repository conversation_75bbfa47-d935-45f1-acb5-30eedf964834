import { ReadOnlyEBookService } from '@/proxy/holy-bless/books';
import { EBookDto } from '@/proxy/holy-bless/books/dtos';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { ChannelIdContentCodeService } from '@/services/channelIdContentCode.service';
import { I18nService } from '@/services/i18n.service';
import { LoadingService } from '@/services/loading.service';
import { CommonModule } from '@angular/common';
import { Component, HostListener, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { PaginatorModule } from 'primeng/paginator';
import { skip, Subscription } from 'rxjs';

@Component({
  selector: 'app-ebook-card',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    CardModule,
    FormsModule,
    PaginatorModule,
    ButtonModule,
  ],
  templateUrl: './ebook-card.html',
  styleUrls: ['./ebook-card.scss'],
})
export class EbookCardComponent {
  route = inject(ActivatedRoute);
  #ReadOnlyEBookService = inject(ReadOnlyEBookService);
  router = inject(Router);

  totalRecords = 0;
  rows = 10;
  first = 0;
  private _isMobile = false;
  collectionId: string | null = null;
  cardItems = signal<EBookDto[]>([]);
  channelIdContentCodeService = inject(ChannelIdContentCodeService);
  subs = new Subscription();
  i18nService = inject(I18nService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  loadingService = inject(LoadingService);

  constructor() {
    this.checkMobile();
  }

  ngOnInit() {
    this.route.params.subscribe({
      next: (param) => {
        const channelId = param['channelId'];
        if (channelId) {
          this.loadEBooks(channelId);
          this.changeLanguage(channelId);
        }
      },
    });
  }

  changeLanguage(channelId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe(() => {
      this.#ReadOnlyChannelService.getMatchedChannel(channelId).subscribe({
        next: (channel) => {
          if (!channel) {
            this.router.navigateByUrl(`/landing`);
            return;
          }
          if (channel.id !== channelId) {
            this.router.navigateByUrl(`/ebooks/ebook-card/${channel.id}`);
          }
        },
      });
    });
    this.subs.add(sub);
  }

  loadEBooks(channelId: number) {
    if (!channelId) return;
    this.loadingService.show();
    this.#ReadOnlyEBookService.getEBooksByChannelId(channelId).subscribe({
      next: (data) => {
        this.cardItems.set(data);
        this.totalRecords = data.length;
        this.loadingService.hide();
      },
      error: (error) => {
        console.error('获取电子书数据失败:', error);
        this.loadingService.hide();
      },
    });
  }

  // 检测是否为移动端
  get isMobile(): boolean {
    return this._isMobile;
  }

  // 根据设备类型返回每页条数选项
  get rowsPerPageOptions(): number[] | undefined {
    return this.isMobile ? undefined : [10, 20, 50];
  }

  private checkMobile() {
    this._isMobile = window.innerWidth <= 768;
  }

  openBook(id: number | undefined) {
    if (!id) return;
    this.router.navigateByUrl(`/ebooks/book-detail/${id}`);
  }

  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    console.log('页面变化:', event);
    // 这里可以添加数据加载逻辑
  }

  // 监听窗口大小变化
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkMobile();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
