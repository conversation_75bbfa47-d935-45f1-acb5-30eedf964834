import { DragDropModule } from '@angular/cdk/drag-drop';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TableModule } from 'primeng/table';
import { cloneDeep } from 'lodash-es';
import { contentCategoryOptions } from '@/proxy/holy-bless/enums/content-category.enum';
import { DatePickerModule } from 'primeng/datepicker';
import { ConfirmationService } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { TableDisplayPipe } from '../../pipes/table-display.pipe';
import { ArticleService } from '@/proxy/holy-bless/articles/article.service';
import { publishStatusOptions } from '@/proxy/holy-bless/enums/publish-status.enum';
import { articleContentCategoryOptions } from '@/proxy/holy-bless/enums/article-content-category.enum';
import { Router } from '@angular/router';
import { DrawerModule } from 'primeng/drawer';
import { TreeSelectModule } from 'primeng/treeselect';
import { CollectionService } from '@/proxy/holy-bless/collections/collection.service';
import { BuildFlatData, BuildTreeData } from '../../libs/utils';
import { EBookService } from '@/proxy/holy-bless/books/ebook.service';
import { ChapterService } from '@/proxy/holy-bless/books/chapter.service';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-article',
  templateUrl: './article.html',
  styleUrls: ['./article.scss'],
  standalone: true,
  imports: [
    TableModule,
    ButtonModule,
    DragDropModule,
    SelectButtonModule,
    FormsModule,
    InputTextModule,
    DialogModule,
    SelectModule,
    DatePickerModule,
    ConfirmPopupModule,
    TableDisplayPipe,
    DrawerModule,
    TreeSelectModule,
  ],
  providers: [ConfirmationService],
})
export class ArticleComponent {
  #ArticleService = inject(ArticleService);
  confirmationService = inject(ConfirmationService);
  #CollectionService = inject(CollectionService);
  #EBookService = inject(EBookService);
  #ChapterService = inject(ChapterService);
  router = inject(Router);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);
  publishStatusOptions = computed(() =>
    publishStatusOptions.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  articleContentCategoryOptions = computed(() =>
    articleContentCategoryOptions.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  columns = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID') },
    { field: 'title', header: this.i18nService.t()('Title') },
    {
      field: 'status',
      header: this.i18nService.t()('Status'),
      type: 'select',
      selectData: this.publishStatusOptions(),
    },
    {
      field: 'articleContentCategory',
      header: this.i18nService.t()('ContentCategory'),
      type: 'select',
      selectData: this.articleContentCategoryOptions(),
    },
    { field: 'views', header: this.i18nService.t()('Views') },
  ]);
  first = signal(0);
  rows = signal(20);
  totalRecords = signal(0);

  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');

  data = signal<any[]>([]);
  selectedRows = signal<any[]>([]);

  // drawer
  drawerVisible = signal(false);
  subData = signal<any[]>([]);
  selectSub = signal<any[]>([]);

  // add to collection
  addToCollectionVisible = signal(false);
  selectCollection = signal<any>(null);
  collections = signal<any[]>([]);
  collectionTree = signal<any[]>([]);
  collectionMap = new Map();

  // add to chapter
  addToChapterVisible = signal(false);
  selectEbook = signal<any>(null);
  ebookList = signal<any[]>([]);
  selectChapter = signal<any>(null);
  chapterTree = signal<any[]>([]);
  chapterMap = new Map();

  constructor() {
    effect(() => {
      this.loadChapterData(this.selectEbook());
    });
  }

  ngOnInit() {
    this.loadData();
    this.loadCollections();
    this.loadEbookData();
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#ArticleService
      .getList({
        skipCount: this.first(),
        maxResultCount: this.rows(),
        languageCode: this.lang(),
      })
      .subscribe({
        next: (res) => {
          this.data.set(res.items || []);
          this.totalRecords.set(res.totalCount || 0);
          this.#LoadingService.loading.set(false);
        },
        error: () => {
          this.#LoadingService.loading.set(false);
        },
      });
  }

  loadCollections() {
    this.#CollectionService.getAllCollections(this.lang()).subscribe((res) => {
      this.collectionTree.set(
        BuildTreeData(
          res || [],
          'parentCollectionId',
          undefined,
          undefined,
          this.collectionMap,
        ),
      );
    });
  }

  loadEbookData() {
    this.#EBookService.getAllEBooks(this.lang()).subscribe((res) => {
      this.ebookList.set(res || []);
    });
  }

  loadChapterData(ebookId: number) {
    if (!ebookId) return;
    this.#ChapterService.getChapterTreeByEBookId(ebookId).subscribe((res) => {
      const list: any[] = [];
      BuildFlatData(res || [], list);

      this.chapterTree.set(
        BuildTreeData(
          list,
          'parentChapterId',
          undefined,
          'title',
          this.chapterMap,
        ),
      );
    });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.loadData();
  }

  contentCategoryOptions = contentCategoryOptions;
  selectData = signal<any>(null);

  mode = signal<'create' | 'edit'>('create');

  handleEdit(rowData: any) {
    this.router.navigateByUrl(`/article-detail/${rowData.id}`);
  }

  handleCreate() {
    this.router.navigateByUrl('/article-detail/create');
  }

  updateSelectData() {
    if (this.mode() === 'edit') {
      this.#ArticleService
        .update(this.selectData().id, this.selectData())
        .subscribe({
          next: (res) => {
            this.loadData();
            this.selectData.set(null);
          },
        });
    } else {
      this.#ArticleService.create(this.selectData()).subscribe({
        next: (res) => {
          this.loadData();
          this.selectData.set(null);
        },
      });
    }
  }

  handleDelete(event: Event, rowData: any) {
    this.confirmationService.confirm({
      target: event.currentTarget as EventTarget,
      message: this.i18nService.t()('DoYouWantToDeleteThisRecord'),
      icon: 'pi pi-info-circle',
      rejectButtonProps: {
        label: this.i18nService.t()('Cancel'),
        severity: 'secondary',
        outlined: true,
      },
      acceptButtonProps: {
        label: this.i18nService.t()('Delete'),
        severity: 'danger',
      },
      accept: () => {
        this.#ArticleService.delete(rowData.id).subscribe({
          next: (res) => {
            this.loadData();
          },
        });
      },
    });
  }

  handleAdd(rowData: any) {
    this.drawerVisible.set(true);
  }

  handleRemove() {}

  handleAddToCollection() {
    this.addToCollectionVisible.set(true);
  }

  handleChangeCollection() {}

  handleAddToChapter() {
    this.addToChapterVisible.set(true);
  }

  handleChangeChapter() {}
}
