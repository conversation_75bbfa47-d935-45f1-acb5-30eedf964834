<p-toast />

<p-blockUI [blocked]="loadingService.loading()" class="custom-loading">
  <div class="w-full flex flex-col items-center justify-center h-screen">
    <i class="pi pi-spin pi-spinner text-[#fff]" style="font-size: 4rem"></i>
    <p class="mt-4 text-white">loading...</p>
  </div>
</p-blockUI>

<div class="flex p-6 h-screen">
  <p-menu
    [model]="items()"
    class="flex justify-center"
    styleClass="w-full !min-w-[11rem]"
  >
    <ng-template #start>
      <span class="inline-flex items-center gap-1 px-2 py-2">
        <img src="logo.svg" class="w-8" alt="" />
      </span>
    </ng-template>
    <ng-template #item let-item>
      <a pRipple class="flex items-center p-menu-item-link rounded-lg" [ngClass]="{'bg-primary-200': this.currentRoute().includes(item.key)}">
        <span [class]="item.icon"></span>
        <span class="ml-2">{{ item.label }}</span>
      </a>
    </ng-template>
  </p-menu>
  <div class="flex-1 ml-3 flex flex-col">
    <div class="flex justify-end items-center gap-2 border-b pb-3">
      <!-- <i class="pi pi-bell"></i> -->
      <p-menu
        #menu2
        [model]="displayLanguages()"
        [popup]="true"
        appendTo="body"
      />
      <p-button
        (click)="menu2.toggle($event)"
        [label]="i18nService.currentLanguageInfo().label"
        [text]="true"
        size="small"
        class="language-button"
      />
      <p-avatar
        image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png"
        class="mr-2"
        shape="circle"
      />
    </div>
    <div class="flex-1 overflow-auto flex">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
