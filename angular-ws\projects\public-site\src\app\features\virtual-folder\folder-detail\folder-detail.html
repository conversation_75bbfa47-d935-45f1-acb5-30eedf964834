<div class="flex flex-1 flex-col md:flex-row">
  @if((mobileService.isMobile | async)) {
  <app-mobile-catagory-files-drawer
    [leftTemplate]="treeTemplate"
    [showRightIcon]="false"
    [leftDrawerVisible]="leftDrawerVisible"
  />
  } @else {
  <ng-container *ngTemplateOutlet="treeTemplate"></ng-container>
  }
  <div
    class="p-6 flex-1 overflow-y-auto"
    [class.w-screen]="mobileService.isMobile | async"
    style="height: calc(100vh - 5rem)"
  >
    <div class="flex items-center gap-2">
      <p-button
        icon="pi pi-cloud-download"
        [label]="i18nService.translate('download')"
        [outlined]="true"
        (onClick)="downloadService.batchDownloadAsZip(selectedProducts)"
      />
      <p-button
        icon="pi pi-play-circle"
        [label]="i18nService.translate('play')"
        [outlined]="true"
        (onClick)="playerService.multiPlayVideo(selectedProducts)"
      />
    </div>
    <p-table
      [value]="products()"
      [(selection)]="selectedProducts"
      size="small"
      stripedRows
    >
      <ng-template #header>
        <tr>
          <th style="width: 2rem"><p-tableHeaderCheckbox /></th>
          <th style="width: 2rem"></th>
          <th>{{ i18nService.translate('name') }}</th>
          <th>{{ i18nService.translate('lastModifiedTime') }}</th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template #body let-product let-index="rowIndex">
        <tr>
          <td>
            <p-tableCheckbox [value]="product" />
          </td>
          <td>{{ index + 1 }}</td>
          <td>{{ product.title || (product.fileName | removeExtension) }}</td>
          <td>
            {{ product.lastModificationTime | date: 'yyyy-MM-dd' }}
          </td>
          <td>
            <div class="flex items-center gap-2">
              <p-button
                size="small"
                icon="pi pi-cloud-download"
                [outlined]="true"
                [appDownload]="product.fileUrl"
                [downloadName]="product.fileName"
              />
              <p-button
                size="small"
                icon="pi pi-play-circle"
                [outlined]="true"
                (onClick)="playVideo(product)"
              />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template #footer> </ng-template>
    </p-table>
    <div class="flex justify-end mt-4">
      <p-paginator
        [rows]="10"
        [totalRecords]="totalRecords()"
        [first]="first()"
        (onPageChange)="onPageChange($event)"
      />
    </div>
  </div>
</div>

<ng-template #treeTemplate>
  <p-tree
    [value]="files()"
    [styleClass]="`${(mobileService.isMobile | async) ? 'w-full' : 'w-[20rem]'} h-full`"
    selectionMode="single"
    [(selection)]="selectedNode"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    (onNodeSelect)="loadFiles($event.node)"
  >
    <ng-template let-node pTemplate="folder">
      <p class="flex items-center gap-2">
        {{ node.label }}
        <i
          class="pi pi-cloud-download folder-download"
          (click)="folderDownload($event, node)"
        ></i>
      </p>
    </ng-template>
  </p-tree>
</ng-template>
