import { Component, inject, computed, effect } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { BlockUIModule } from 'primeng/blockui';
import { MenuModule } from 'primeng/menu';
import { LoadingService } from '../services/loading.service';
import { ToastModule } from 'primeng/toast';
import { I18nService } from '../services/i18n.service';
import { TieredMenuModule } from 'primeng/tieredmenu';
import { ButtonModule } from 'primeng/button';
import { toSignal } from '@angular/core/rxjs-interop';
import { filter, map, startWith } from 'rxjs/operators';
import { NavigationEnd } from '@angular/router';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    MenuModule,
    AvatarModule,
    BlockUIModule,
    ToastModule,
    TieredMenuModule,
    ButtonModule,
    NgClass
],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = 'admin';

  router = inject(Router);
  loadingService = inject(LoadingService);
  i18nService = inject(I18nService);
  route = inject(ActivatedRoute);

  constructor() {
    effect(() => {
      console.log('currentRoute',this.currentRoute())
    })
  }

  // 监听路由变化
  currentRoute = toSignal(
    this.router.events.pipe(
      filter((event) => event instanceof NavigationEnd),
      map(() => this.router.url),
      startWith(this.router.url),
    ),
    { initialValue: this.router.url },
  );

  // 辅助方法判断是否为活跃路由
  private isActiveRoute(routePath: string): boolean {
    return this.currentRoute()?.includes(routePath) || false;
  }

  displayLanguages = computed((): MenuItem[] =>
    this.i18nService.supportedLanguages().map((lang) => ({
      label: lang.label,
      command: () => this.i18nService.language.set(lang.code),
    })),
  );

  items = computed((): MenuItem[] => [
    {
      key: '/bucket-file',
      label: this.i18nService.t()('BucketFile'),
      icon: 'pi pi-file',
      command: () => this.router.navigate(['/bucket-file']),
    },
    {
      key: '/article',
      label: this.i18nService.t()('Article'),
      icon: 'pi pi-file-pdf',
      command: () => this.router.navigate(['/article']),
    },
    {
      key: '/collection',
      label: this.i18nService.t()('Collection'),
      icon: 'pi pi-database',
      command: () => this.router.navigate(['/collection']),
    },
    {
      key: '/chapter',
      label: this.i18nService.t()('Chapter'),
      icon: 'pi pi-align-left',
      command: () => this.router.navigate(['/chapter']),
    },
    {
      key: '/ebook',
      label: this.i18nService.t()('Ebook'),
      icon: 'pi pi-book',
      command: () => this.router.navigate(['/ebook']),
    },
    {
      key: '/album',
      label: this.i18nService.t()('Album'),
      icon: 'pi pi-tiktok',
      command: () => this.router.navigate(['/album']),
    },
    {
      key: '/folder',
      label: this.i18nService.t()('VirtualDisk'),
      icon: 'pi pi-folder',
      command: () => this.router.navigate(['/folder']),
    },
    {
      key: '/channel',
      label: this.i18nService.t()('Channel'),
      icon: 'pi pi-objects-column',
      command: () => this.router.navigate(['/channel']),
    },
  ]);
}
