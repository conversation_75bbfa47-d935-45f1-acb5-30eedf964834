<div>
  <div class="mt-6">
    @if (primaryArticleFiles.length > 0) {
    <h3>{{i18nService.translate('media_playback')}}</h3>
    @for (item of primaryArticleFiles; track $index) {
    <p class="flex justify-between items-center mt-3">
      <span>{{ item.fileName | removeExtension }}</span>
      <i class="pi pi-play-circle cursor-pointer" (click)="playMedia(item)"></i>
    </p>
    } }
  </div>
  <div class="mt-6">
    @if (notImageArticleFiles.length > 0) {
    <h3>{{i18nService.translate('attachment_download')}}</h3>
    @for (item of notImageArticleFiles; track $index) {
    <p class="flex justify-between items-center mt-3">
      <span>{{ item.fileName | removeExtension }}</span>
      <i
        class="pi pi-cloud-download cursor-pointer"
        [appDownload]="item.fileUrl!"
        [downloadName]="item.fileName"
      ></i>
    </p>
    } }
  </div>
</div>
