<p-menubar
  [model]="menuItems"
  [style.display]="!(mobileService.isMobile | async) ? 'none' : 'block'"
>
  <!-- 左侧 Logo -->
  <ng-template pTemplate="start">
    <div
      class="flex align-items-center cursor-pointer"
      (click)="navigateToHome()"
    >
      <img src="assets/images/logo.svg" class="w-10" alt="" srcset="" />
    </div>
  </ng-template>

  <!-- 右侧控制区域 -->
  <ng-template pTemplate="end">
    <div class="control-area">
      <!-- 页面展示语言选择 -->
      <div class="language-selector">
        <i class="pi pi-language" tooltipPosition="bottom"></i>
        <p-menu
          #menu2
          [model]="displayLanguages()"
          [popup]="true"
          appendTo="body"
        />
        <p-button
          (click)="menu2.toggle($event)"
          [label]="i18nService.currentLanguageInfo().label"
          [text]="true"
          size="small"
          class="language-button"
        />
      </div>

      <!-- 设置按钮 -->
      <p-button
        icon="pi pi-cog"
        [text]="true"
        [rounded]="true"
        severity="secondary"
        [pTooltip]="i18nService.translate('common.settings')"
        tooltipPosition="bottom"
        (onClick)="drawerService.openSettings()"
      >
      </p-button>

      <!-- 播放/暂停按钮 -->
      <p-button
        icon="pi pi-play-circle"
        [text]="true"
        [rounded]="true"
        severity="primary"
        [pTooltip]="i18nService.translate('common.play')"
        tooltipPosition="bottom"
        (onClick)="drawerService.openPlayer()"
      >
      </p-button>
    </div>
  </ng-template>
</p-menubar>

@if (!(mobileService.isMobile | async)) {
  <div class="flex gap-2 relative bg-[#fff] py-2 custom-menu p-menubar">
    <div class="ml-2">
      <div
        class="flex align-items-center cursor-pointer"
        (click)="navigateToHome()"
      >
        <img src="assets/images/logo.svg" class="w-10" alt="" srcset="" />
      </div>
    </div>
    <div class="flex-1 flex">
      @for (item of menuItems; track $index) {
        <p
          class="p-menubar-item-content custom-menu-item"
          (click)="handleMenuItem(item)"
          (mouseenter)="toggleSubMenu(item)"
        >
          <a class="p-menubar-item-link">
            {{ item.label }}
            @if (item.items && item.items.length > 0) {
              <i class="pi pi-angle-down"></i>
            }
          </a>
        </p>
      }
    </div>
    <div class="mr-2">
      <div class="control-area">
        <!-- 页面展示语言选择 -->
        <div class="language-selector">
          <i class="pi pi-language" tooltipPosition="bottom"></i>
          <p-menu
            #menu2
            [model]="displayLanguages()"
            [popup]="true"
            appendTo="body"
          />
          <p-button
            (click)="menu2.toggle($event)"
            [label]="i18nService.currentLanguageInfo().label"
            [text]="true"
            size="small"
            class="language-button"
          />
        </div>

        <!-- 设置按钮 -->
        <p-button
          icon="pi pi-cog"
          [text]="true"
          [rounded]="true"
          severity="secondary"
          [pTooltip]="i18nService.translate('settings')"
          tooltipPosition="bottom"
          (onClick)="drawerService.openSettings()"
        >
        </p-button>

        <!-- 播放/暂停按钮 -->
        <p-button
          icon="pi pi-play-circle"
          [text]="true"
          [rounded]="true"
          severity="primary"
          [pTooltip]="i18nService.translate('play')"
          tooltipPosition="bottom"
          (onClick)="drawerService.openPlayer()"
        >
        </p-button>
      </div>
    </div>
  </div>
  @if (activeItem() && activeItem()!.items && activeItem()!.items!.length > 0) {
    <div
      class="absolute w-screen custom-submenu p-2 shadow-lg z-10 rounded-b-2xl"
      (mouseleave)="activeItem.set(null)"
    >
      <div class="flex gap-2">
        @for (item of activeItem()!.items; track $index) {
          <div class="flex-1 flex flex-col">
            <p
              class="font-bold px-2 hover:bg-gray-100 p-2 rounded-lg cursor-pointer custom-menu-item"
              (click)="item.command?.({})"
            >
              {{ item.label }}
            </p>
            @for (item of item.items; track $index) {
              <p
                class="px-2 hover:bg-gray-100 p-2 rounded-lg cursor-pointer custom-menu-item"
                (click)="item.command?.({})"
              >
                {{ item.label }}
              </p>
            }
          </div>
        }
      </div>
    </div>
  }
}
