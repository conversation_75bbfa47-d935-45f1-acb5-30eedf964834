<div class="px-6 relative h-10 flex items-center mt-2">
  @if(showLeftIcon) {
  <i class="pi pi-book !text-xl absolute left-6" (click)="onLeftDrawerShow()"></i>
  } @if(showRightIcon) {
  <i class="pi pi-paperclip !text-xl absolute right-6" (click)="onRightDrawerShow()"></i>
  }
</div>

<!-- tree -->
@if((mobileService.isMobile | async)) {
<p-drawer
  [(visible)]="leftDrawerVisible"
  position="left"
  [style]="{ width: '100vw' }"
>
  <ng-container *ngTemplateOutlet="leftTemplate"></ng-container>
</p-drawer>
}

<!-- 附件下载 -->
@if((mobileService.isMobile | async)) {
<p-drawer
  [(visible)]="rightDrawerVisible"
  position="right"
  [style]="{ width: '100vw' }"
>
  <ng-container *ngTemplateOutlet="rightTemplate"></ng-container>
</p-drawer>
}
