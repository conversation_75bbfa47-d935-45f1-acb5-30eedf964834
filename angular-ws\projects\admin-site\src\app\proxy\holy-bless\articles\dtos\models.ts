import type { AuditedEntityDto, EntityDto, PagedAndSortedResultRequestDto } from '@abp/ng.core';
import type { ArticleContentCategory } from '../../enums/article-content-category.enum';
import type { PublishStatus } from '../../enums/publish-status.enum';
import type { MediaType } from '../../enums/media-type.enum';
import type { ContentCategory } from '../../enums/content-category.enum';
import type { SearchFieldEnum } from '../../enums/search-field-enum.enum';

export interface ArticleAdminSearchDto extends PagedAndSortedResultRequestDto {
  languageCode?: string;
}

export interface ArticleDto extends AuditedEntityDto<number> {
  deliveryDate?: string;
  languageCode?: string;
  title?: string;
  thumbnailFileId?: number;
  thumbnailUrl?: string;
  description?: string;
  keywords?: string;
  views: number;
  likes: number;
  articleContentCategory?: ArticleContentCategory;
  status?: PublishStatus;
  content?: string;
  memo?: string;
}

export interface ArticleFileDto extends EntityDto<number> {
  articleId: number;
  articleTitle?: string;
  fileId: number;
  fileName?: string;
  mediaType?: MediaType;
  contentType?: ContentCategory;
  title?: string;
  description?: string;
  isPrimary: boolean;
}

export interface ArticleFileSearchDto extends PagedAndSortedResultRequestDto {
  articleId?: number;
}

export interface ArticleSearchDto extends PagedAndSortedResultRequestDto {
  keyword?: string;
  searchFields?: SearchFieldEnum[];
  contentCategories?: ArticleContentCategory[];
  deliveryDateStart?: string;
  deliveryDateEnd?: string;
}

export interface ArticleSearchResultDto {
  id: number;
  title?: string;
  description?: string;
  deliveryDate?: string;
  articleContentCategory?: ArticleContentCategory;
  lastModificationTime?: string;
  content?: string;
}

export interface ArticleTitleDto extends EntityDto<number> {
  title?: string;
  deliveryDate?: string;
}

export interface ArticleToTagDto extends EntityDto {
  articleId: number;
  tagId: number;
}

export interface CreateUpdateArticleDto {
  deliveryDate: string;
  languageCode: string;
  title: string;
  thumbnailFileId?: number;
  description?: string;
  keywords?: string;
  articleContentCategory?: ArticleContentCategory;
  status?: PublishStatus;
  content?: string;
  memo?: string;
}

export interface CreateUpdateArticleFileDto {
  articleId: number;
  fileId: number;
  title?: string;
  description?: string;
  isPrimary: boolean;
}

export interface TeacherArticleLinkDto {
  teacherArticleId: number;
  weight: number;
}
