import { DragDropModule } from '@angular/cdk/drag-drop';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TableModule } from 'primeng/table';
import { cloneDeep } from 'lodash-es';
import { BucketFileService } from '@/proxy/holy-bless/buckets/bucket-file.service';
import { contentCategoryOptions } from '@/proxy/holy-bless/enums/content-category.enum';
import { DatePickerModule } from 'primeng/datepicker';
import { ConfirmationService, FilterMatchMode } from 'primeng/api';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { TableDisplayPipe } from '../../pipes/table-display.pipe';
import { TreeSelectModule } from 'primeng/treeselect';
import { VirtualFolderService } from '@/proxy/holy-bless/virtual-folders/virtual-folder.service';
import { BuildTreeData } from '../../libs/utils';
import { AlbumService } from '@/proxy/holy-bless/albums/album.service';
import { ArticleService } from '@/proxy/holy-bless/articles/article.service';
import { LoadingService } from '../../services/loading.service';
import { I18nService } from '../../services/i18n.service';

@Component({
  selector: 'app-bucket-file',
  templateUrl: './bucket-file.html',
  styleUrls: ['./bucket-file.scss'],
  standalone: true,
  imports: [
    TableModule,
    ButtonModule,
    DragDropModule,
    SelectButtonModule,
    FormsModule,
    InputTextModule,
    DialogModule,
    SelectModule,
    DatePickerModule,
    ConfirmPopupModule,
    TableDisplayPipe,
    TreeSelectModule,
  ],
  providers: [ConfirmationService],
})
export class BucketFileComponent {
  #BucketFileService = inject(BucketFileService);
  #VirtualFolderService = inject(VirtualFolderService);
  #AlbumService = inject(AlbumService);
  #ArticleService = inject(ArticleService);
  confirmationService = inject(ConfirmationService);
  #LoadingService = inject(LoadingService);
  i18nService = inject(I18nService);
  contentCategoryOptions = computed(() =>
    contentCategoryOptions.map((item) => ({
      ...item,
      key: this.i18nService.t()(item.key),
    })),
  );

  columns = computed(() => [
    { field: 'id', header: this.i18nService.t()('ID') },
    {
      field: 'fileName',
      header: this.i18nService.t()('FileName'),
      filter: true,
    },
    {
      field: 'contentCategory',
      header: this.i18nService.t()('ContentCategory'),
      type: 'select',
      selectData: this.contentCategoryOptions(),
      filter: true,
      matchMode: FilterMatchMode.EQUALS,
      hideApplyButton: true,
    },
    {
      field: 'relativePathInBucket',
      header: this.i18nService.t()('RelativePath'),
    },
    {
      field: 'deliveryDate',
      header: this.i18nService.t()('DeliveryDate'),
      type: 'date',
      matchMode: FilterMatchMode.BEFORE,
      filter: true,
    },
  ]);
  first = signal(0);
  rows = signal(20);
  totalRecords = signal(0);

  langOptions = computed(() => [
    { label: this.i18nService.t()('zh-Hans'), value: 'zh-Hans' },
    { label: this.i18nService.t()('zh-Hant'), value: 'zh-Hant' },
    { label: this.i18nService.t()('en'), value: 'en' },
  ]);
  lang = signal<string>('zh-Hans');

  filter = {};

  data = signal<any[]>([]);
  selectedRows = signal<any[]>([]);

  // add to folder
  addToFolderVisible = signal(false);
  selectFolder = signal<any>(null);
  folderTree = signal<any[]>([]);
  folderMap = new Map();

  // add to album
  addToAlbumVisible = signal(false);
  selectAlbum = signal<any>(null);
  albumList = signal<any[]>([]);

  // add to article
  addToArticleVisible = signal(false);
  selectArticle = signal<any[]>([]);
  articleList = signal<any[]>([]);
  articleFirst = signal(0);
  articleRows = signal(10);
  articleTotalRecords = signal(0);

  constructor() {
    effect(() => {
      console.log('this.selectedRows()', this.selectedRows());
    });
  }

  ngOnInit() {
    this.loadData();
    this.loadFolderData();
    this.loadAlbumData();
  }

  loadData() {
    this.#LoadingService.loading.set(true);
    this.#BucketFileService
      .getList({
        skipCount: this.first(),
        maxResultCount: this.rows(),
        languageCode: this.lang(),
        ...this.filter,
      })
      .subscribe({
        next: (res) => {
          this.data.set(res.items || []);
          this.totalRecords.set(res.totalCount || 0);
          this.#LoadingService.loading.set(false);
        },
        error: () => {
          this.#LoadingService.loading.set(false);
        },
      });
  }

  handleFilter(event: any) {
    const { filters } = event;
    const contentCategories = filters['contentCategory'][0]?.value || undefined;
    const fileName = filters['fileName'][0]?.value || undefined;
    const deliveryDateStart =
      filters['deliveryDate'].find(
        (i: any) => i.matchMode === FilterMatchMode.AFTER,
      )?.value || undefined;
    const deliveryDateEnd =
      filters['deliveryDate'].find(
        (i: any) => i.matchMode === FilterMatchMode.BEFORE,
      )?.value || undefined;

    this.filter = {
      contentCategory: contentCategories,
      fileName,
      deliveryDateStart,
      deliveryDateEnd,
    };

    this.first.set(0);
    this.loadData();
  }

  loadFolderData() {
    this.#VirtualFolderService.getAllVirtualFolders(this.lang()).subscribe({
      next: (res) => {
        this.folderTree.set(
          BuildTreeData(
            res || [],
            'parentFolderId',
            'id',
            'folderName',
            this.folderMap,
          ),
        );
      },
    });
  }

  loadAlbumData() {
    this.#AlbumService.getAllAlbums(this.lang()).subscribe({
      next: (res) => {
        this.albumList.set(res || []);
      },
    });
  }

  loadArticleData() {
    this.#ArticleService
      .getList({
        skipCount: this.articleFirst(),
        maxResultCount: this.articleRows(),
        languageCode: this.lang(),
      })
      .subscribe({
        next: (res) => {
          this.articleList.set(res.items || []);
          this.articleTotalRecords.set(res.totalCount || 0);
        },
      });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.loadData();
  }

  selectData = signal<any>(null);

  mode = signal<'create' | 'edit'>('create');

  handleEdit(rowData: any) {
    this.selectData.set(cloneDeep(rowData));
    this.mode.set('edit');
  }

  handleCreate() {
    this.selectData.set({});
    this.mode.set('create');
  }

  updateSelectData() {
    if (this.mode() === 'edit') {
      this.#BucketFileService
        .update(this.selectData().id, this.selectData())
        .subscribe({
          next: (res) => {
            this.loadData();
            this.selectData.set(null);
          },
        });
    } else {
      this.#BucketFileService.create(this.selectData()).subscribe({
        next: (res) => {
          this.loadData();
          this.selectData.set(null);
        },
      });
    }
  }

  handleDelete(event: Event, rowData: any) {
    this.confirmationService.confirm({
      target: event.currentTarget as EventTarget,
      message: this.i18nService.t()('DoYouWantToDeleteThisRecord'),
      icon: 'pi pi-info-circle',
      rejectButtonProps: {
        label: this.i18nService.t()('Cancel'),
        severity: 'secondary',
        outlined: true,
      },
      acceptButtonProps: {
        label: this.i18nService.t()('Delete'),
        severity: 'danger',
      },
      accept: () => {
        this.#BucketFileService.delete(rowData.id).subscribe({
          next: (res) => {
            this.loadData();
          },
        });
      },
    });
  }

  handleAddToFolder() {
    this.addToFolderVisible.set(true);
  }

  handleChangeFolder() {}

  handleAddToAlbum() {
    this.addToAlbumVisible.set(true);
  }

  handleChangeAlbum() {}

  handleAddToArticle() {
    this.addToArticleVisible.set(true);
    this.loadArticleData();
  }

  onArticlePageChange(event: any) {
    this.articleFirst.set(event.first);
    this.loadArticleData();
  }

  handleChangeArticle() {}

  getMatchModeOptions(type: string) {
    switch (type) {
      case 'text':
        return [{ label: 'Contains', value: FilterMatchMode.CONTAINS }];
      case 'date':
        return [
          { label: 'Date before', value: FilterMatchMode.BEFORE },
          { label: 'Date after', value: FilterMatchMode.AFTER },
        ];
      case 'select':
        return [{ label: 'Equals', value: FilterMatchMode.EQUALS }];
      default:
        return [{ label: 'Contains', value: FilterMatchMode.CONTAINS }];
    }
  }
}
