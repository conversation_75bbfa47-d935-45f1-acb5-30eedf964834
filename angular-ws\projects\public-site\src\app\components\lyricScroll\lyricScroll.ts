import {
  Component,
  computed,
  ElementRef,
  inject,
  Input,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';

export interface LyricLine {
  time: number; // 时间戳，单位为秒
  text: string; // 歌词文本
}

@Component({
  selector: 'app-lyric-scroll',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './lyricScroll.html',
})
export class LyricScrollComponent {
  @Input() currentTime = signal(0); // 当前播放时间
  activeIndex = computed(() => {
    return this.findIndex(this.currentTime());
  });
  offset = computed(() => {
    return this.setOffset(this.currentTime());
  });

  lrcData: LyricLine[] = [];
  ngOnInit() {
    const content = `
[00:12.39]你的“心意自我”，什么都做不了
[00:18.19]实相中，你什么都没有做
[00:23.90]现实中，做与所做的“你”
[00:27.46]只是恐惧的心
[00:30.33]在寻求安宁的居所
[00:34.00]
[00:35.27]将你的身心意识，交付于圣主
[00:40.97]体验身心内在， 深邃的平安与温暖
[00:47.22]你真实的身份，只是纯净的“灵”
[00:52.38]你原本的心态，就是无限的“爱”
[01:00.00]
[01:00.74]让现实的自我，沉浸在爱之中
[01:06.35]当无限温暖的安宁之爱， 熄灭了自我
[01:12.75]平安之中， 将苏醒“你”的永恒
[01:22.00]
[01:53.97]你的“心意自我”,什么都做不了
[01:59.89]实相中，你什么都没有做
[02:05.62]现实中，做与所做的“你”
[02:09.88]只是恐惧的心，在寻求安宁的居所
[02:16.00]
[02:16.92]将你的身心意识，交付于圣主
[02:22.49]体验身心内在，深邃的平安与温暖
[02:28.60]你真实的身份，只是纯净的“灵”
[02:34.38]你原本的心态，就是无限的“爱”
[02:41.00]
[02:42.22]让现实的自我，沉浸在爱之中
[02:47.89]当无限温暖的安宁之爱， 熄灭了自我
[02:54.32]平安之中， 将苏醒“你”的永恒
[03:03.32]
    `;
    this.parseLrc(content);
  }
  parseLrc(lrcContent: string) {
    const lines = lrcContent.split('\n');
    const res = [] as LyricLine[];

    for (let i = 0; i < lines.length; i++) {
      const str = lines[i].trim();
      if (!str) continue; // 跳过空行
      const timeMatch = str.match(/\[(\d{2}):(\d{2}\.\d{2})\]/);
      const text = str.replace(/\[\d{2}:\d{2}\.\d{2}\]/g, '').trim();
      if (text === '') continue; // 跳过没有歌词文本的行
      if (!timeMatch) continue; // 跳过没有时间戳的行
      const minutes = parseInt(timeMatch[1], 10);
      const seconds = parseFloat(timeMatch[2]);
      const timeInSeconds = minutes * 60 + seconds;
      const obj: LyricLine = { time: timeInSeconds, text };
      // 提取歌词文本
      res.push(obj);
    }
    this.lrcData = res;
  }

  findIndex(currentTime: number): number {
    for (let i = 0; i < this.lrcData.length; i++) {
      if (currentTime < this.lrcData[i].time) {
        return i - 1;
      }
    }
    return this.lrcData.length - 1;
  }

  elementRef = inject(ElementRef);

  getContainerHeight(): number {
    const element = this.elementRef.nativeElement as HTMLElement;
    return element.offsetHeight;
  }

  getLiHeight() {
    const firstLi = this.elementRef.nativeElement.querySelector('li');
    return firstLi ? firstLi.offsetHeight : 0;
  }
  setOffset(currentTime: number) {
    const containerHeight = this.getContainerHeight();
    const liHeight = this.getLiHeight();
    const index = this.findIndex(currentTime);
    let offset = liHeight * index + liHeight / 2 - containerHeight / 2;
    if (offset < 0) offset = 0;
    return offset;
  }
}
