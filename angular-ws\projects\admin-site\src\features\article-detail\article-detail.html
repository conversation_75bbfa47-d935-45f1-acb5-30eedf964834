<div class="flex p-6 overflow-auto relative" style="height: calc(100vh - 10rem);">
  <div class="form flex-1 pr-4">
    <div class="form-item">
      <label>{{ i18nService.t()('ArticleTitle') }}</label>
      <input type="text" pInputText [(ngModel)]="data().title" />
    </div>
    <div class="form-item">
      <label>
        {{ i18nService.t()('ArticleContent') }}
      </label>
      <p-editor
        [(ngModel)]="data().content"
        [style]="{'height':'320px'}"
        placeholder="Enter your content here..."
      >
      </p-editor>
    </div>
    <div class="form-item">
      <label>
        {{ i18nService.t()('ArticleDescription') }}
      </label>
      <textarea
        name="description"
        id=""
        [(ngModel)]="data().description"
        pTextarea
      ></textarea>
    </div>
    <div class="form-item">
      <label>
        {{ i18nService.t()('ArticleKeywords') }}
      </label>
      <div class="flex flex-col p-inputtext input-tag-container">
        <div class="flex gap-2">
          @for (item of data().keywords; track $index) {
          <p-chip
            [label]="item"
            [removable]="true"
            (onRemove)="removeKeyword($index)"
          ></p-chip>
          }
        </div>
        <input
          type="text"
          pInputText
          (keyup.enter)="handleAddKeyword($event)"
          class="!border-0 !shadow-none !px-0 !py-2 input-tag"
        />
      </div>
    </div>
  </div>
  <div class="form pl-4 sticky top-0 border-l border-gray-300">
    <div class="form-item">
      <label>{{ i18nService.t()('Thumbnail') }}</label>
      <input type="text" pInputText [(ngModel)]="data().thumbnailFileId" />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('ContentCategory') }}</label>
      <p-select
        [options]="articleContentCategoryOptions"
        [(ngModel)]="data().articleContentCategory"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('Status') }}</label>
      <p-select
        [options]="publishStatusOptions"
        [(ngModel)]="data().status"
        optionLabel="key"
        optionValue="value"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>
        {{ i18nService.t()('DeliveryDate') }}
      </label>
      <p-datepicker
        styleClass="w-full"
        inputId="calendar-24h"
        [(ngModel)]="data().deliveryDate"
        [showTime]="true"
        hourFormat="24"
        dateFormat="yy-mm-dd"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{ i18nService.t()('LastModifierId(Readonly)') }}</label>
      <input
        type="text"
        pInputText
        [(ngModel)]="data().lastModifierId"
        readonly
      />
    </div>
  </div>
</div>
<div class="flex justify-end gap-2 absolute bottom-0 right-0">
  <p-button [label]="i18nService.t()('Cancel')" severity="secondary" (onClick)="handleCancel()"></p-button>
  <p-button [label]="i18nService.t()('Save')" (onClick)="handleUpdate()"></p-button>
</div>
