import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { providePrimeNG } from 'primeng/config';
import {
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import { provideAbpOAuth } from '@abp/ng.oauth';
import { provideAbpCore, withOptions } from '@abp/ng.core';
import { environment } from '../environments/environment';
import { RequestHeaderInterceptor } from '../interceptors/request-header.interceptor';
import { MyPreset } from './my-theme';
import { ErrorHandlerInterceptor } from '../interceptors/error-handler.interceptor';
import { MessageService } from 'primeng/api';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideAnimationsAsync(),
    provideHttpClient(
      withFetch(),
      withInterceptors([RequestHeaderInterceptor, ErrorHandlerInterceptor]),
    ),
    providePrimeNG({
      theme: {
        preset: MyPreset,
      },
    }),
    provideAbpCore(
      withOptions({
        environment,
        registerLocaleFn: () =>
          new Promise((resolve) => {
            resolve(null);
          }),
      }),
    ),
    provideAbpOAuth(),
    MessageService,
  ],
};
