import { inject, Injectable } from '@angular/core';
import { DrawerService } from './drawer.service';
import { BehaviorSubject } from 'rxjs';
import { VideoCacheService } from './video-cache.service';

@Injectable({ providedIn: 'root' })
export class PlayerService {
  drawerService = inject(DrawerService);
  videoCacheService = inject(VideoCacheService);

  #playerList = new BehaviorSubject<any[]>([]);
  playerList$ = this.#playerList.asObservable();
  #activeItem = new BehaviorSubject<any | null>(null);
  activeItem$ = this.#activeItem.asObservable();

  constructor() {
    this.loadPlayerList();
  }

  loadPlayerList() {
    const playerList: any[] = this.getPlayerList();
    this.#playerList.next(playerList);
  }

  async playVideo(player: any) {
    if (!player || !player.fileUrl) {
      console.error('No video player provided');
      return;
    }
    this.drawerService.openPlayer();
    const playerList = this.storePlayerToList(player);
    this.#playerList.next(playerList);
    this.#activeItem.next(player);
  }

  multiPlayVideo(playerList: any[]) {
    if (!Array.isArray(playerList) || playerList.length === 0) {
      console.error('No video players provided');
      return;
    }
    this.drawerService.openPlayer();
    for (let i = playerList.length - 1; i >= 0; i--) {
      this.storePlayerToList(playerList[i]);
    }
    const updatedList: any[] = this.getPlayerList();
    this.#playerList.next(updatedList);
    this.#activeItem.next(updatedList[0]);
  }

  storePlayerToList(player: any) {
    const playerList: any[] = this.getPlayerList();
    if (!player || !player.fileUrl) {
      console.error('No video player provided');
      return playerList;
    }
    const find = playerList.find((item) => item.fileId === player.fileId);
    if (find) {
      return playerList;
    }
    playerList.unshift(player);
    this.videoCacheService.smartCacheVideo(player.fileUrl, player.fileName);
    this.setPlayerList(playerList);
    return playerList;
  }

  getPlayerList() {
    const playerList: any[] = JSON.parse(
      localStorage.getItem('playerList') || '[]',
    );
    return playerList;
  }

  setPlayerList(playerList: any[]) {
    if (!Array.isArray(playerList)) {
      console.error('Invalid player list provided');
      return;
    }
    localStorage.setItem('playerList', JSON.stringify(playerList));
  }

  removeFromPlayerList(player: any) {
    if (!player || !player.fileId) {
      console.error('No video player provided');
      return;
    }
    const playerList = this.getPlayerList();
    const updatedList = playerList.filter(
      (item) => item.fileId !== player.fileId,
    );
    this.setPlayerList(updatedList);
    this.#playerList.next(updatedList);
  }

  clearPlayerList() {
    this.setPlayerList([]);
    this.#playerList.next([]);
  }
}
