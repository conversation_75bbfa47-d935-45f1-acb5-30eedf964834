<div class="p-4 EbookContainer relative overflow-hidden" id="EbookContainer">
  <div class="flex justify-between">
    <p-selectbutton
      [options]="langOptions()"
      [(ngModel)]="lang"
      optionLabel="label"
      optionValue="value"
      (onChange)="loadData()"
    >
    </p-selectbutton>
    <div class="flex gap-2">
      <p-button
        [text]="true"
        [label]="i18nService.t()('Create')"
        (onClick)="handleCreate()"
      ></p-button>
      <p-button
        [text]="true"
        severity="danger"
        [label]="i18nService.t()('Delete')"
      ></p-button>
      <p-button
        [text]="true"
        [label]="i18nService.t()('AddToChannel')"
        (onClick)="handleAddToChannel()"
        [disabled]="!selectedRows().length"
      ></p-button>
    </div>
  </div>
  <p-table
    [value]="data()"
    [columns]="columns()"
    [tableStyle]="{'min-width': '50rem'}"
    [(selection)]="selectedRows"
    stripedRows
  >
    <ng-template #header let-columns>
      <tr>
        <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
        @for(col of columns; track col.field) {
        <th pReorderableColumn>
          <div class="flex items-center">
            {{col.header}}
            <p-columnFilter type="text" [field]="col.field" display="menu" />
          </div>
        </th>
        }
        <th style="width: 8rem"></th>
      </tr>
    </ng-template>
    <ng-template #body let-rowData let-columns="columns" let-index="rowIndex">
      <tr
        [pReorderableRow]="index"
        class="cursor-pointer"
        (click)="handleRowClick(rowData)"
      >
        <td>
          <p-tableCheckbox [value]="rowData" />
        </td>
        @for(col of columns; track col.field) {
        <td>
          {{rowData[col.field] | tableDisplay: {type: col.type, selectData:
          col.selectData} }}
        </td>
        }
        <td>
          <p-button
            [text]="true"
            styleClass="!rounded-full"
            icon="pi pi-pencil"
            (onClick)="handleEdit(rowData)"
          ></p-button>
          <p-button
            [text]="true"
            styleClass="!rounded-full"
            icon="pi pi-trash"
            severity="danger"
            (onClick)="handleDelete($event, rowData)"
          ></p-button>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-confirmpopup />

<p-dialog
  [header]="mode() === 'edit' ? i18nService.t()('Edit') : i18nService.t()('Create')"
  [modal]="true"
  [(visible)]="selectData"
  [style]="{ width: '25rem' }"
>
  @if(selectData()) {
  <div class="form">
    <div class="form-item">
      <label> {{i18nService.t()('Title')}} </label>
      <input type="text" pInputText [(ngModel)]="selectData().title" />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Channel')}}</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectData().channel"
        [options]="channelTree()"
        appendTo="body"
      />
    </div>
    <div class="form-item">
      <label>{{i18nService.t()('Thumbnail')}}</label>
      <input
        type="text"
        pInputText
        [(ngModel)]="selectData().thumbnailFileId"
      />
    </div>
    <div class="form-item">
      <label> {{i18nService.t()('Description')}} </label>
      <input type="text" pInputText [(ngModel)]="selectData().description" />
    </div>
  </div>
  }
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="updateSelectData()"
      [label]="i18nService.t()('Confirm')"
    ></p-button>
  </div>
</p-dialog>

<p-dialog
  [header]="i18nService.t()('AddToChannel')"
  [modal]="true"
  [(visible)]="addToChannelVisible"
  [style]="{ width: '25rem' }"
>
  <div class="form">
    <div class="form-item">
      <label>{{i18nService.t()('Channel')}}</label>
      <p-treeselect
        containerStyleClass="w-full"
        [(ngModel)]="selectChannel"
        [options]="channelTree()"
        appendTo="body"
      />
    </div>
  </div>
  <div class="w-full grid grid-cols-2 gap-2 mt-4">
    <p-button
      styleClass="w-full"
      severity="secondary"
      [label]="i18nService.t()('Cancel')"
    ></p-button>
    <p-button
      styleClass="w-full"
      (onClick)="changeChannel()"
      [label]="i18nService.t()('Confirm')"
    ></p-button>
  </div>
</p-dialog>
